// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		3175BF502E0091E300CD05BB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3175BF3A2E0091E000CD05BB /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3175BF412E0091E000CD05BB;
			remoteInfo = WavyAudio;
		};
		3175BF5A2E0091E300CD05BB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3175BF3A2E0091E000CD05BB /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3175BF412E0091E000CD05BB;
			remoteInfo = WavyAudio;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		3175BF422E0091E000CD05BB /* WavyAudio.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = WavyAudio.app; sourceTree = BUILT_PRODUCTS_DIR; };
		3175BF4F2E0091E300CD05BB /* WavyAudioTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = WavyAudioTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3175BF592E0091E300CD05BB /* WavyAudioUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = WavyAudioUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		3190F9712E0574C900772EE8 /* Exceptions for "WavyAudio" folder in "WavyAudio" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 3175BF412E0091E000CD05BB /* WavyAudio */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3175BF442E0091E000CD05BB /* WavyAudio */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				3190F9712E0574C900772EE8 /* Exceptions for "WavyAudio" folder in "WavyAudio" target */,
			);
			path = WavyAudio;
			sourceTree = "<group>";
		};
		3175BF522E0091E300CD05BB /* WavyAudioTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = WavyAudioTests;
			sourceTree = "<group>";
		};
		3175BF5C2E0091E300CD05BB /* WavyAudioUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = WavyAudioUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		3175BF3F2E0091E000CD05BB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3175BF4C2E0091E300CD05BB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3175BF562E0091E300CD05BB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3175BF392E0091E000CD05BB = {
			isa = PBXGroup;
			children = (
				3175BF442E0091E000CD05BB /* WavyAudio */,
				3175BF522E0091E300CD05BB /* WavyAudioTests */,
				3175BF5C2E0091E300CD05BB /* WavyAudioUITests */,
				3175BF432E0091E000CD05BB /* Products */,
			);
			sourceTree = "<group>";
		};
		3175BF432E0091E000CD05BB /* Products */ = {
			isa = PBXGroup;
			children = (
				3175BF422E0091E000CD05BB /* WavyAudio.app */,
				3175BF4F2E0091E300CD05BB /* WavyAudioTests.xctest */,
				3175BF592E0091E300CD05BB /* WavyAudioUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3175BF412E0091E000CD05BB /* WavyAudio */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3175BF632E0091E300CD05BB /* Build configuration list for PBXNativeTarget "WavyAudio" */;
			buildPhases = (
				3175BF3E2E0091E000CD05BB /* Sources */,
				3175BF3F2E0091E000CD05BB /* Frameworks */,
				3175BF402E0091E000CD05BB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				3175BF442E0091E000CD05BB /* WavyAudio */,
			);
			name = WavyAudio;
			packageProductDependencies = (
			);
			productName = WavyAudio;
			productReference = 3175BF422E0091E000CD05BB /* WavyAudio.app */;
			productType = "com.apple.product-type.application";
		};
		3175BF4E2E0091E300CD05BB /* WavyAudioTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3175BF662E0091E300CD05BB /* Build configuration list for PBXNativeTarget "WavyAudioTests" */;
			buildPhases = (
				3175BF4B2E0091E300CD05BB /* Sources */,
				3175BF4C2E0091E300CD05BB /* Frameworks */,
				3175BF4D2E0091E300CD05BB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3175BF512E0091E300CD05BB /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3175BF522E0091E300CD05BB /* WavyAudioTests */,
			);
			name = WavyAudioTests;
			packageProductDependencies = (
			);
			productName = WavyAudioTests;
			productReference = 3175BF4F2E0091E300CD05BB /* WavyAudioTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		3175BF582E0091E300CD05BB /* WavyAudioUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3175BF692E0091E300CD05BB /* Build configuration list for PBXNativeTarget "WavyAudioUITests" */;
			buildPhases = (
				3175BF552E0091E300CD05BB /* Sources */,
				3175BF562E0091E300CD05BB /* Frameworks */,
				3175BF572E0091E300CD05BB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3175BF5B2E0091E300CD05BB /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3175BF5C2E0091E300CD05BB /* WavyAudioUITests */,
			);
			name = WavyAudioUITests;
			packageProductDependencies = (
			);
			productName = WavyAudioUITests;
			productReference = 3175BF592E0091E300CD05BB /* WavyAudioUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3175BF3A2E0091E000CD05BB /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					3175BF412E0091E000CD05BB = {
						CreatedOnToolsVersion = 26.0;
					};
					3175BF4E2E0091E300CD05BB = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 3175BF412E0091E000CD05BB;
					};
					3175BF582E0091E300CD05BB = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 3175BF412E0091E000CD05BB;
					};
				};
			};
			buildConfigurationList = 3175BF3D2E0091E000CD05BB /* Build configuration list for PBXProject "WavyAudio" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 3175BF392E0091E000CD05BB;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 3175BF432E0091E000CD05BB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3175BF412E0091E000CD05BB /* WavyAudio */,
				3175BF4E2E0091E300CD05BB /* WavyAudioTests */,
				3175BF582E0091E300CD05BB /* WavyAudioUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3175BF402E0091E000CD05BB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3175BF4D2E0091E300CD05BB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3175BF572E0091E300CD05BB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		3175BF3E2E0091E000CD05BB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3175BF4B2E0091E300CD05BB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3175BF552E0091E300CD05BB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		3175BF512E0091E300CD05BB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3175BF412E0091E000CD05BB /* WavyAudio */;
			targetProxy = 3175BF502E0091E300CD05BB /* PBXContainerItemProxy */;
		};
		3175BF5B2E0091E300CD05BB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3175BF412E0091E000CD05BB /* WavyAudio */;
			targetProxy = 3175BF5A2E0091E300CD05BB /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		3175BF612E0091E300CD05BB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		3175BF622E0091E300CD05BB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		3175BF642E0091E300CD05BB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = WavyAudio/WavyAudio.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = WavyAudio/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.WavyAudio;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "WavyAudio/WavyAudio-Bridging-Header.h";
				SWIFT_VERSION = 6.2;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		3175BF652E0091E300CD05BB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = WavyAudio/WavyAudio.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = WavyAudio/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.WavyAudio;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "WavyAudio/WavyAudio-Bridging-Header.h";
				SWIFT_UPCOMING_FEATURE_APPROACHABLE_CONCURRENCY = YES;
				SWIFT_UPCOMING_FEATURE_DEFAULT_ACTOR_ISOLATION = YES;
				SWIFT_UPCOMING_FEATURE_STRICT_MEMORY_SAFETY = YES;
				SWIFT_VERSION = 6.2;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
		3175BF672E0091E300CD05BB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.WavyAudioTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 6.2;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/WavyAudio.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/WavyAudio";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		3175BF682E0091E300CD05BB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.WavyAudioTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 6.2;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/WavyAudio.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/WavyAudio";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
		3175BF6A2E0091E300CD05BB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.WavyAudioUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 6.2;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = WavyAudio;
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		3175BF6B2E0091E300CD05BB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.WavyAudioUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 6.2;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = WavyAudio;
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3175BF3D2E0091E000CD05BB /* Build configuration list for PBXProject "WavyAudio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3175BF612E0091E300CD05BB /* Debug */,
				3175BF622E0091E300CD05BB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3175BF632E0091E300CD05BB /* Build configuration list for PBXNativeTarget "WavyAudio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3175BF642E0091E300CD05BB /* Debug */,
				3175BF652E0091E300CD05BB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3175BF662E0091E300CD05BB /* Build configuration list for PBXNativeTarget "WavyAudioTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3175BF672E0091E300CD05BB /* Debug */,
				3175BF682E0091E300CD05BB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3175BF692E0091E300CD05BB /* Build configuration list for PBXNativeTarget "WavyAudioUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3175BF6A2E0091E300CD05BB /* Debug */,
				3175BF6B2E0091E300CD05BB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 3175BF3A2E0091E000CD05BB /* Project object */;
}
