# Project Plan: Rust Integration for WAVYAUDIO DAW

## Project Goals

1. **Drag-and-Drop File Handling**: Enable macOS drag & drop integration for audio file imports
2. **Real-time Audio Engines**: Develop Rust/CoreAudio backend for low-level audio processing
3. **Loop & Song Management**: Create track scheduling with BPM synchronization and looping capabilities
4. **Live Session Broadcasting**: Implement virtual MIDI controllers + live session output routing
5. **Splice Bridge Support**: Integrate with Ark DAW's standard for sample library management
6. **Cross-Language Bridging**: Ensure safe Rust-Swift value passing through bridging headers + async communication

## Feature Breakdown - Rust Components

1. **AudioFileManager**:
   - Path+Metadata parsing
   - BPM detection interface
   - Splice sample compatibility

2. **TrackController**:
   - Track list management
   - Playback sequencing
   - Export pipelines

3. **BPM Analysis Library**:
   - ArkAudio BPM detection
   - FFT-based tempo analysis

4. **Audio Renderer**:
   - Mixdown engine
   - Audio buffer allocation

## Integration Points - Rust w/ SwiftUI

1. **Core Foundation Types**: Use `CFURL` for file path conversion
2. **Bridging Header**: Define Rust -> Swift communication patterns
3. **Async Handlers**: Pass completion callbacks from Swift async dispatch
4. **Metal Performance Shim**: For live GLSL processing via WAVYENGINE

## Technical Considerations

1. **Memory Safety**: All Rust imports use `Path` for safe file operations
2. **Event Loop Management**: Integrate e.g. `tokio` between Rust threads + SwiftUI main thread
3. **Audio Buffer Synchronization**: Share buffer pointers between Rust engine + WAVYAUDIO UI elements

## Development Pipeline

1. Initialize Rust modules (audiofile.rs + bpm_analysis.rs)
2. Implement `TrackManager` with load_files + schedule_track
3. Set up macOS drop delegate in ContentView.swift
4. Create test Splice samples (WAV + tags)
5. Validate Ark BPM analysis library in mock environments

## Roadmap

**Week 1**:
- Rust init + module scaffolding
- Set up WAVYAUDIO bridging
- Implement initial drag/drop

**Week 2**:
- Core Audio engine setup
- BPM detection integration
- Track playlist management

**Week 3**:
- Build Splice Bridge protocol
- Implement UI elements - wave forms
- MIDI sync testing

**Week 4**:
- Full track export pipeline
- Metal shader integration
- Comprehensive DAW testing

## Assumptions

- Existing CoreAudio HAL integrations (from WAVYAUDIO)
- Splice Bridge API documentation available in Ark Arkdocs
- Ark BPMLib.framework compiled for ARM64/x86_64

## Deliverables

1. Drag-enabled SwiftUI project
2. Rust project with complete audio engines
3. Cross-language bridging documentation
4. Loopable/exported sample assets

# END PROJECTPLAN.md
