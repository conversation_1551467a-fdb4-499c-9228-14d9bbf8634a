<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ark-loopback CLI Mockup</title>
    <style>
        body { font-family: "Menlo", "Consolas", monospace; background-color: #2d2d2d; color: #dcdcdc; margin: 0; padding: 20px; }
        .cli-container { border: 1px solid #555; border-radius: 5px; padding: 20px; }
        .prompt { color: #6a9fb5; }
        .command { color: #a5c261; }
        .output { color: #dcdcdc; }
    </style>
</head>
<body>
    <div class="cli-container">
        <p><span class="prompt">$</span> <span class="command">./target/release/ark list</span></p>
        <pre class="output">
Available Audio Devices:
  0 - MacBook Pro Speakers (0 in, 2 out)
  1 - External Headphones (0 in, 2 out)
  2 - Zoom Audio Device (2 in, 2 out)
  3 - DJPro Virtual Output (2 in, 0 out)
        </pre>
        <p><span class="prompt">$</span> <span class="command">./target/release/ark dj-to-zoom</span></p>
        <pre class="output">
Setting up DJPro → Zoom loopback...
✓ DJPro output now routed to Zoom input
        </pre>
        <p><span class="prompt">$</span> <span class="command">./target/release/ark status</span></p>
        <pre class="output">
Current Audio Routing:
  DJPro Virtual Output → Zoom Audio Device (2 channels)
        </pre>
    </div>
</body>
</html>
