//
// ContentView.swift - Main Application Interface
// WWDC Compliance Reference:
// - WWDC25-286: SwiftUI fundamentals (NavigationSplitView, modern layouts)
// - WWDC25-268: Swift 6 concurrency (@Observable environment, @StateObject migration)
// - WWDC25-319: SwiftUI essentials (View composition, performance)
// - WWDC23-10165: Fix failures faster with Xcode test reports (UI automation)
//
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-286
//

import SwiftUI

struct ContentView: View {
    @Environment(AudioEngineManager.self) private var audioManager
    @Environment(UndoRedoManager.self) private var undoRedoManager
    @Environment(DeviceManager.self) private var deviceManager
    @State private var selectedDevice: AudioDevice?
    @State private var showingPresets = false
    @State private var showingSettings = false
    @State private var showingErrorRecovery = false
    @State private var searchText = ""
    
    var body: some View {
        NavigationSplitView {
            DeviceListSidebar(
                selectedDevice: $selectedDevice,
                deviceManager: deviceManager
            )
            .navigationTitle("Audio Devices")
            .frame(minWidth: 280)
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Menu {
                        Button("Refresh Devices", systemImage: "arrow.clockwise") {
                            Task {
                                await audioManager.refreshDevices()
                            }
                        }
                        
                        Divider()
                        
                        Button("Settings", systemImage: "gear") {
                            showingSettings.toggle()
                        }
                        
                        if audioManager.hasConflicts {
                            Button("Resolve Conflicts", systemImage: "exclamationmark.triangle") {
                                showingErrorRecovery.toggle()
                            }
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
        } content: {
            AudioRoutingCanvas(
                selectedDevice: selectedDevice,
                devices: audioManager.availableDevices,
                connections: audioManager.activeConnections
            )
            .navigationTitle("Audio Routing")
            // Toolbar temporarily disabled due to SwiftUI macOS Sequoia environment crash
            // TODO: Re-enable when SwiftUI fixes AppKitToolbarStrategy environment access
        } detail: {
            QuickPresetPanel(
                showingPresets: $showingPresets,
                selectedDevice: selectedDevice
            )
            .navigationTitle("Controls")
        }
        .navigationSplitViewStyle(.balanced)
        .searchable(text: $searchText, placement: .sidebar)
        .sheet(isPresented: $showingPresets) {
            PresetManagerView()
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingErrorRecovery) {
            ErrorRecoveryView(conflicts: audioManager.systemConflicts)
        }
        .alert("Engine Error", isPresented: .constant(audioManager.hasEngineError)) {
            Button("Retry") {
                Task {
                    try? await audioManager.initializeEngine()
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text(audioManager.engineErrorMessage)
        }
        .task {
            await initializeAudioEngine()
        }
        .onReceive(NotificationCenter.default.publisher(for: .audioDeviceListChanged)) { _ in
            Task {
                await audioManager.refreshDevices()
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func initializeAudioEngine() async {
        do {
            try await audioManager.initializeEngine()
        } catch {
            print("Failed to initialize audio engine: \(error)")
        }
    }
    
    private func clearAllConnections() async {
        // Clear all active connections
        await audioManager.clearAllConnections()
    }
}

// MARK: - Supporting Views

struct PresetManagerView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("Preset Manager")
                    .font(.largeTitle)
                    .padding()
                
                Text("Audio routing presets will be available here")
                    .foregroundStyle(.secondary)
                
                Spacer()
            }
            .navigationTitle("Presets")

            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var bufferSize: Double = 256
    @State private var sampleRate: Double = 48000
    @State private var enableMetalAcceleration = true
    
    var body: some View {
        NavigationView {
            Form {
                Section("Audio Processing") {
                    VStack(alignment: .leading) {
                        Text("Buffer Size: \(Int(bufferSize)) samples")
                        Slider(value: $bufferSize, in: 64...2048, step: 64)
                    }
                    
                    VStack(alignment: .leading) {
                        Text("Sample Rate: \(Int(sampleRate)) Hz")
                        Slider(value: $sampleRate, in: 44100...192000, step: 44100)
                    }
                }
                
                Section("Performance") {
                    Toggle("Metal Acceleration", isOn: $enableMetalAcceleration)
                    Toggle("Apple Silicon Optimization", isOn: .constant(true))
                        .disabled(true)
                }
                
                Section("System") {
                    HStack {
                        Text("Audio Latency")
                        Spacer()
                        Text("< 10ms")
                            .foregroundStyle(.secondary)
                    }
                    
                    HStack {
                        Text("CPU Usage")
                        Spacer()
                        Text("< 20%")
                            .foregroundStyle(.secondary)
                    }
                }
            }
            .navigationTitle("Settings")

            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ErrorRecoveryView: View {
    @Environment(\.dismiss) private var dismiss
    let conflicts: [SystemConflict]
    
    var body: some View {
        NavigationView {
            List(conflicts, id: \.description) { conflict in
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: conflictIcon(for: conflict.severity))
                            .foregroundStyle(conflictColor(for: conflict.severity))
                        
                        Text(conflict.type.description)
                            .font(.headline)
                    }
                    
                    Text(conflict.description)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    if !conflict.affectedDevices.isEmpty {
                        Text("Affected devices: \(conflict.affectedDevices.count)")
                            .font(.caption2)
                            .foregroundStyle(.tertiary)
                    }
                }
                .padding(.vertical, 4)
            }
            .navigationTitle("System Conflicts")

            .toolbar {
                ToolbarItem(placement: .confirmationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func conflictIcon(for severity: ConflictSeverity) -> String {
        switch severity {
        case .info: return "info.circle"
        case .warning: return "exclamationmark.triangle"
        case .error: return "xmark.circle"
        case .critical: return "exclamationmark.octagon"
        }
    }
    
    private func conflictColor(for severity: ConflictSeverity) -> Color {
        switch severity {
        case .info: return .blue
        case .warning: return .orange
        case .error: return .red
        case .critical: return .purple
        }
    }
}

// MARK: - ConflictType Extension

extension ConflictType {
    var displayDescription: String {
        switch self {
        case .arkDaemon: return "ARK Daemon Conflict"
        case .soundSource: return "SoundSource Conflict"
        case .otherAudioApp: return "Other Audio Application"
        case .multipleInputSources: return "Multiple Input Sources"
        case .sampleRateMismatch: return "Sample Rate Mismatch"
        case .exclusiveAccess: return "Exclusive Access Conflict"
        case .bufferSizeConflict: return "Buffer Size Conflict"
        case .multipleAudioApps: return "Multiple Audio Apps"
        case .unsignedDrivers: return "Unsigned Drivers"
        case .spatialAudioConflict: return "Spatial Audio Conflict"
        case .driverCompatibilityIssue: return "Driver Compatibility"
        case .audioSessionConflict: return "Audio Session Conflict"
        case .threadPriorityIssue: return "Thread Priority Issue"
        case .clockDomainConflict: return "Clock Domain Conflict"
        }
    }
}

#Preview {
    ContentView()
        .environment(AudioEngineManager())
}