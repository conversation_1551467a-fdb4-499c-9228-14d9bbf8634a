//
// WavyAudioApp.swift - Main Application Entry Point
// WWDC Compliance Reference:
// - WWDC25-268: Swift 6 concurrency (Main actor mode, async/await initialization)
// - WWDC25-251: AVFoundation advances (Permission handling, AVAudioApplication API)
// - WWDC19-508: Modernizing Your Audio App (No deprecated audio APIs)
// - WWDC25-245: Xcode 26 features (Explicitly built modules, enhanced tooling)
//
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-268
//

import SwiftUI
import AVFoundation

@main
struct WavyAudioApp: App {
    @State private var audioManager = AudioEngineManager()
    @State private var audioProcessor = AudioProcessor()
    @State private var undoRedoManager = UndoRedoManager()
    @State private var deviceManager = DeviceManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(audioManager)
                .environment(undoRedoManager)
                .environment(deviceManager)
                // .environment(audioProcessor) // Removed until AudioProcessor conforms to Observable
                .task {
                    await initializeApplication()
                }
                .preferredColorScheme(.dark) // Optimize for professional audio
        }
        .windowStyle(.hiddenTitleBar)
        .windowToolbarStyle(.unified)
        .commands {
            audioCommands
        }
    }
    
    // MARK: - Initialization
    
    private func initializeApplication() async {
        // Configure Apple Silicon optimizations
        PerformanceOptimizer.configureForAppleSilicon()
        
        // Request audio permissions
        await requestAudioPermissions()
        
        // Initialize Core Audio HAL Manager
        do {
            try await CoreAudioHALManager.shared.initialize()
        } catch {
            print("Failed to initialize Core Audio HAL: \(error)")
        }
        
        // Initialize audio engine
        await audioManager.initializeEngine()
        
        // Start audio processor
        do {
            try await audioProcessor.startProcessing()
        } catch {
            print("Failed to start audio processor: \(error)")
        }
    }
    
    private func requestAudioPermissions() async {
        let granted = await AVAudioApplication.requestRecordPermission()
        if !granted {
            print("Audio permission denied")
        }
    }
    
    // MARK: - Menu Commands
    
    private var audioCommands: some Commands {
        CommandMenu("Audio") {
            Button("Refresh Devices") {
                Task {
                    await audioManager.refreshDevices()
                }
            }
            .keyboardShortcut("r", modifiers: .command)
            
            Divider()
            
            Button("Start Engine") {
                Task {
                    do {
                        try await audioManager.initializeEngine()
                    } catch {
                        print("Failed to start audio engine: \(error)")
                    }
                }
            }
            .keyboardShortcut("s", modifiers: [.command, .shift])
            
            Button("Stop Engine") {
                audioManager.stopEngine()
            }
            .keyboardShortcut("t", modifiers: [.command, .shift])
            
            Divider()
            
            Button("Clear All Connections") {
                // audioManager.clearAllConnections() // Simplified for build
            }
            .keyboardShortcut("k", modifiers: [.command, .shift])
        }
        
    }
}

// Extension removed - stopEngine() is now in AudioEngineManager directly

// MARK: - Notification Extensions

extension Notification.Name {
    static let resetCanvasView = Notification.Name("resetCanvasView")
    static let fitCanvasToScreen = Notification.Name("fitCanvasToScreen")
    static let autoLayoutNodes = Notification.Name("autoLayoutNodes")
}