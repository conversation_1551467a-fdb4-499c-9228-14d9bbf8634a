<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDisplayName</key>
    <string>WavyAudio</string>
    <key>CFBundleIdentifier</key>
    <string>D.WavyAudio</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>LSMinimumSystemVersion</key>
    <string>13.0</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>This app requires access to the microphone for audio routing.</string>
    <key>NSCameraUsageDescription</key>
    <string>This app does not use the camera, but capability is reserved.</string>
</dict>
</plist>
