import SwiftUI

struct ConnectionInfoView: View {
    let deviceCount: Int
    let connectionCount: Int
    let zoomScale: CGFloat
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            Text("Devices: \(deviceCount)")
                .font(.caption)
                .foregroundStyle(.secondary)
            
            Text("Connections: \(connectionCount)")
                .font(.caption)
                .foregroundStyle(.secondary)
            
            Text("Zoom: \(Int(zoomScale * 100))%")
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .padding(8)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 8))
    }
}