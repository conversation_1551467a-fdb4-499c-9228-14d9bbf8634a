import SwiftUI

struct SystemStatusIndicator: View {
    @Environment(DeviceManager.self) var deviceManager: DeviceManager

    var body: some View {
        HStack {
            if !deviceManager.systemConflicts.isEmpty {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.yellow)
                    .onTapGesture {
                        // Show an alert with the conflict details
                        let conflict = deviceManager.systemConflicts.first! // Simplified for example
                        let alert = NSAlert()
                        alert.messageText = "System Conflict Detected"
                        alert.informativeText = conflict.description
                        alert.alertStyle = .warning
                        alert.addButton(withTitle: "OK")
                        alert.runModal()
                    }
            }
            Text("System OK")
        }
    }
}

#Preview {
    SystemStatusIndicator()
        .environment(DeviceManager())
}