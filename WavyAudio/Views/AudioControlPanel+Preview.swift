import SwiftUI
import AVFoundation

struct AudioControlPanel_Previews: PreviewProvider {
    static var previews: some View {
        let audioEngine = AudioEngineManager()
        let deviceManager = DeviceManager()
        
        AudioControlPanel()
            .environmentObject(audioEngine)
            .environmentObject(deviceManager)
            .frame(width: 400, height: 500)
            .padding()
    }
}

// MARK: - Preview Helpers

// Mock AudioEngineManager for previews
class PreviewAudioEngine: AudioEngineManager {
    override init() {
        super.init()
        // Initialize with mock data for preview
        self.currentLatency = 0.005 // 5ms
        self.cpuUsage = 35.7
        self.bufferSize = 256
    }
}

// Mock DeviceManager for previews
class PreviewDeviceManager: DeviceManager {
    override init() {
        super.init()
        // Add some mock devices for preview
        self.devices = [
            AudioDevice(
                id: "device1",
                name: "Built-in Output",
                manufacturer: "Apple Inc.",
                type: .output,
                isDefault: true
            ),
            AudioDevice(
                id: "device2",
                name: "External Headphones",
                manufacturer: "Acme Corp",
                type: .output,
                isDefault: false
            )
        ]
    }
}
