import SwiftUI
import CoreAudio
import CoreData
import AVFoundation

struct QuickPresetPanel: View {
    @Binding var showingPresets: Bool
    var selectedDevice: AudioDevice?
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("Quick Presets")
                .font(.headline)
                .padding(.bottom, 5)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 10) {
                    ForEach(0..<5) { index in
                        Button(action: {
                            loadPreset(index)
                        }) {
                            Text("Preset \(index + 1)")
                                .font(.caption)
                                .padding(.horizontal, 10)
                                .padding(.vertical, 5)
                                .background(Color.accentColor.opacity(0.2))
                                .cornerRadius(5)
                        }
                        .buttonStyle(.plain)
                    }
                }
            }
        }
        .padding()
        .background(.ultraThinMaterial)
        .cornerRadius(10)
    }
    
    func loadPreset(_ index: Int) {
        // Placeholder for preset loading logic
        guard selectedDevice != nil else { return }
        
        // On macOS, we use AVAudioApplication for permissions instead of AVAudioSession
        Task {
            let granted = await AVAudioApplication.requestRecordPermission()
            if granted {
                applyPreset(index)
            } else {
                print("Permission denied to apply preset")
            }
        }
    }
    
    func applyPreset(_ index: Int) {
        // Placeholder for applying preset - would involve CoreData fetching
        print("Preset \(index + 1) applied to \(selectedDevice?.name ?? "Unknown Device")")
    }
}

#Preview {
    @Previewable @State var showingPresets = false
    QuickPresetPanel(showingPresets: $showingPresets, selectedDevice: nil)
}
