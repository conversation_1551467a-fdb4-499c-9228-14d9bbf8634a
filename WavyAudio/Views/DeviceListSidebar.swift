
//
// DeviceListSidebar.swift - Audio Device List Interface
// WWDC Compliance Reference:
// - WWDC25-286: SwiftUI fundamentals (NavigationSplitView, List performance)
// - WWDC25-268: Swift 6 concurrency (@Observable instead of @ObservedObject)
// - WWDC25-251: AVFoundation advances (Real-time device switching, route changes)
// - WWDC21-10190: DriverKit Audio (User space audio driver enumeration)
//
// Status: EMPTY IMPLEMENTATION - Requires complete development
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-286
//

import SwiftUI
import CoreAudio
import OSLog

struct DeviceListSidebar: View {
    @Binding var selectedDevice: AudioDevice?
    var deviceManager: DeviceManager
    @State private var searchText: String = ""
    @State private var showDiagnostics = false
    @State private var diagnostics = AudioDiagnostics()
    @State private var lastConflictCheck = Date()
    
    private let logger = Logger(subsystem: "com.wavyaudio.ui", category: "DeviceListSidebar")

    var body: some View {
        VStack(spacing: 0) {
            // Enhanced header with diagnostics toggle
            HStack {
                SearchField(searchText: $searchText)
                
                Button(action: {
                    showDiagnostics.toggle()
                    if showDiagnostics && !diagnostics.isMonitoring {
                        diagnostics.startRealtimeMonitoring()
                    }
                }) {
                    Image(systemName: "stethoscope")
                        .foregroundColor(showDiagnostics ? .accentColor : .secondary)
                }
                .buttonStyle(.borderless)
                .help("Toggle Audio Diagnostics")
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .background(.regularMaterial)
            
            // Enhanced conflict alerts bar with severity-based styling
            if !diagnostics.realtimeConflicts.isEmpty {
                VStack(alignment: .leading, spacing: 6) {
                    ForEach(diagnostics.realtimeConflicts.prefix(3), id: \.id) { conflict in
                        HStack(alignment: .top, spacing: 8) {
                            // Severity icon with appropriate color
                            Image(systemName: conflict.severity.iconName)
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(conflict.severity.color)
                                .frame(width: 20, alignment: .center)
                            
                            // Conflict details
                            VStack(alignment: .leading, spacing: 2) {
                                // Title with type
                                HStack(alignment: .firstTextBaseline) {
                                    Text(conflict.type.rawValue)
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(conflict.severity.color)
                                    
                                    Spacer()
                                    
                                    // Time since conflict was detected
                                    Text(conflict.timestamp, style: .time)
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                                
                                // Description
                                Text(conflict.description)
                                    .font(.caption2)
                                    .foregroundColor(.primary)
                                    .lineLimit(2)
                                    .fixedSize(horizontal: false, vertical: true)
                                
                                // Affected devices if any
                                if !conflict.affectedDevices.isEmpty {
                                    HStack(alignment: .firstTextBaseline, spacing: 4) {
                                        Image(systemName: "speaker.wave.2")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                        
                                        Text("Affected: \(conflict.affectedDevices.joined(separator: ", "))")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                            .lineLimit(1)
                                    }
                                }
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(conflict.severity.color.opacity(0.08))
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 6)
                                .stroke(conflict.severity.color.opacity(0.2), lineWidth: 1)
                        )
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                    
                    // Show count of additional conflicts if any
                    if diagnostics.realtimeConflicts.count > 3 {
                        HStack {
                            Spacer()
                            Text("+\(diagnostics.realtimeConflicts.count - 3) more conflicts...")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 6)
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: diagnostics.realtimeConflicts)
            }

            List(filteredDevices, id: \.id) { device in
                DeviceRowView(
                    device: device,
                    isSelected: selectedDevice?.id == device.id,
                    conflicts: conflictsForDevice(device)
                )
                .contentShape(Rectangle())
                .onTapGesture {
                    selectedDevice = device
                }
            }
            .listStyle(.sidebar)
            .navigationTitle("Devices")
            // Toolbar temporarily disabled due to SwiftUI macOS Sequoia environment crash
            // TODO: Re-enable when SwiftUI fixes AppKitToolbarStrategy environment access
            
            // Diagnostic panel
            if showDiagnostics {
                VStack(alignment: .leading, spacing: 8) {
                    Text("System Diagnostics")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    HStack {
                        Text("Monitoring: \(diagnostics.isMonitoring ? "Active" : "Inactive")")
                            .foregroundColor(diagnostics.isMonitoring ? .green : .secondary)
                        Spacer()
                        Text("\(diagnostics.realtimeConflicts.count) conflicts")
                            .foregroundColor(diagnostics.realtimeConflicts.isEmpty ? .secondary : .orange)
                    }
                    .font(.caption)
                    .padding(.horizontal)
                    
                    if diagnostics.isProfessionalModeActive {
                        HStack {
                            Image(systemName: "music.note")
                                .foregroundColor(.blue)
                            Text("Professional Mode Active")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        .padding(.horizontal)
                    }
                }
                .background(.regularMaterial)
                .cornerRadius(8)
                    .frame(height: 200)
                    .transition(.move(edge: .bottom))
            }
        }
        .onAppear {
            if showDiagnostics {
                diagnostics.startRealtimeMonitoring()
            }
        }
        .onDisappear {
            diagnostics.stopRealtimeMonitoring()
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredDevices: [AudioDevice] {
        if searchText.isEmpty {
            return deviceManager.devices
        } else {
            return deviceManager.devices.filter { device in
                // Basic name and manufacturer search
                device.name.localizedCaseInsensitiveContains(searchText) ||
                device.manufacturer.localizedCaseInsensitiveContains(searchText) ||
                
                // Device type and capabilities search
                device.deviceType.rawValue.localizedCaseInsensitiveContains(searchText) ||
                device.deviceType.accessibilityDescription.localizedCaseInsensitiveContains(searchText) ||
                
                // Channel configuration search
                device.channelDescription.localizedCaseInsensitiveContains(searchText) ||
                
                // Sample rate search
                device.sampleRateDescription.localizedCaseInsensitiveContains(searchText) ||
                
                // Special capability searches
                (searchText.localizedCaseInsensitiveContains("input") && device.canRecord) ||
                (searchText.localizedCaseInsensitiveContains("output") && device.canPlayback) ||
                (searchText.localizedCaseInsensitiveContains("bidirectional") && device.isBidirectional) ||
                (searchText.localizedCaseInsensitiveContains("virtual") && device.isVirtual) ||
                (searchText.localizedCaseInsensitiveContains("aggregate") && device.isAggregate) ||
                (searchText.localizedCaseInsensitiveContains("stereo") && (device.inputChannels == 2 || device.outputChannels == 2)) ||
                (searchText.localizedCaseInsensitiveContains("mono") && (device.inputChannels == 1 || device.outputChannels == 1)) ||
                (searchText.localizedCaseInsensitiveContains("multichannel") && (device.inputChannels > 2 || device.outputChannels > 2))
            }
        }
    }
    
    // MARK: - Diagnostic Helper Methods
    
    private func conflictsForDevice(_ device: AudioDevice) -> [SystemConflict] {
        // Get conflicts from both real-time monitoring and HAL manager
        var deviceConflicts: [SystemConflict] = []
        
        // Real-time conflicts from diagnostics
        let realtimeConflicts = diagnostics.realtimeConflicts.filter { conflict in
            conflict.affectedDevices.contains(device.deviceID)
        }
        deviceConflicts.append(contentsOf: realtimeConflicts)
        
        // Direct HAL conflicts for this specific device
        let halConflicts = CoreAudioHALManager.shared.detectDeviceSpecificConflicts(deviceID: device.deviceID)
        deviceConflicts.append(contentsOf: halConflicts)
        
        return deviceConflicts
    }
    
    @MainActor
    private func generateDiagnosticReport() async {
        logger.info("Generating diagnostic report from UI")
        let report = await diagnostics.generateFullDiagnosticReport()
        
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.plainText]
        panel.nameFieldStringValue = "WavyAudio-Diagnostic-Report-\(Date().formatted(.iso8601.year().month().day())).txt"
        panel.title = "Export Diagnostic Report"
        
        if panel.runModal() == .OK, let url = panel.url {
            do {
                try await diagnostics.exportDiagnosticReport(report, to: url)
                logger.info("Diagnostic report exported successfully")
            } catch {
                logger.error("Failed to export diagnostic report: \(error)")
            }
        }
    }
    
    @MainActor
    private func resetAudioSystem() {
        logger.info("Resetting audio system from UI")
        
        let alert = NSAlert()
        alert.messageText = "Reset Audio System"
        alert.informativeText = "This will restart the Core Audio daemon and may briefly interrupt audio playback. Continue?"
        alert.addButton(withTitle: "Reset")
        alert.addButton(withTitle: "Cancel")
        alert.alertStyle = .warning
        
        if alert.runModal() == .alertFirstButtonReturn {
            Task {
                do {
                    let process = Process()
                    process.launchPath = "/usr/bin/sudo"
                    process.arguments = ["killall", "coreaudiod"]
                    try process.run()
                    process.waitUntilExit()
                    
                    logger.info("Audio system reset completed")
                } catch {
                    logger.error("Failed to reset audio system: \(error)")
                }
            }
        }
    }

// MARK: - Preview

// Preview temporarily disabled due to circular reference
// #if DEBUG
// #Preview {
//     @Previewable @State var selectedDevice: AudioDevice? = nil
//     DeviceListSidebar(selectedDevice: $selectedDevice, deviceManager: DeviceManager())
// }
// #endif
}
