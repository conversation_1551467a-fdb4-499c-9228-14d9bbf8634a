
//
// DeviceListSidebar.swift - Audio Device List Interface
// WWDC Compliance Reference:
// - WWDC25-286: SwiftUI fundamentals (NavigationSplitView, List performance)
// - WWDC25-268: Swift 6 concurrency (@Observable instead of @ObservedObject)
// - WWDC25-251: AVFoundation advances (Real-time device switching, route changes)
// - WWDC21-10190: DriverKit Audio (User space audio driver enumeration)
//
// Status: EMPTY IMPLEMENTATION - Requires complete development
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-286
//

import SwiftUI
import CoreAudio
import OSLog

struct DeviceListSidebar: View {
    @Binding var selectedDevice: AudioDevice?
    var deviceManager: DeviceManager
    @State private var searchText: String = ""
    @State private var showDiagnostics = false
    @State private var diagnostics = AudioDiagnostics()
    @State private var lastConflictCheck = Date()
    
    private let logger = Logger(subsystem: "com.wavyaudio.ui", category: "DeviceListSidebar")

    var body: some View {
        VStack(spacing: 0) {
            // Enhanced header with diagnostics toggle
            HStack {
                SearchField(searchText: $searchText)
                
                Button(action: {
                    showDiagnostics.toggle()
                    if showDiagnostics && !diagnostics.isMonitoring {
                        diagnostics.startRealtimeMonitoring()
                    }
                }) {
                    Image(systemName: "stethoscope")
                        .foregroundColor(showDiagnostics ? .accentColor : .secondary)
                }
                .buttonStyle(.borderless)
                .help("Toggle Audio Diagnostics")
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .background(.regularMaterial)
            
            // Conflict alerts bar
            if !diagnostics.realtimeConflicts.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    ForEach(diagnostics.realtimeConflicts.prefix(3), id: \.description) { conflict in
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                                .font(.caption)
                            Text(conflict.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                    }
                }
                .background(.orange.opacity(0.1))
                .cornerRadius(4)
                    .transition(.slide)
            }

            List(filteredDevices, id: \.id) { device in
                DeviceRowView(
                    device: device,
                    isSelected: selectedDevice?.id == device.id,
                    conflicts: conflictsForDevice(device)
                )
                .contentShape(Rectangle())
                .onTapGesture {
                    selectedDevice = device
                }
            }
            .listStyle(.sidebar)
            .navigationTitle("Devices")
            // Toolbar temporarily disabled due to SwiftUI macOS Sequoia environment crash
            // TODO: Re-enable when SwiftUI fixes AppKitToolbarStrategy environment access
            
            // Diagnostic panel
            if showDiagnostics {
                VStack(alignment: .leading, spacing: 8) {
                    Text("System Diagnostics")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    HStack {
                        Text("Monitoring: \(diagnostics.isMonitoring ? "Active" : "Inactive")")
                            .foregroundColor(diagnostics.isMonitoring ? .green : .secondary)
                        Spacer()
                        Text("\(diagnostics.realtimeConflicts.count) conflicts")
                            .foregroundColor(diagnostics.realtimeConflicts.isEmpty ? .secondary : .orange)
                    }
                    .font(.caption)
                    .padding(.horizontal)
                    
                    if diagnostics.isProfessionalModeActive {
                        HStack {
                            Image(systemName: "music.note")
                                .foregroundColor(.blue)
                            Text("Professional Mode Active")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                        .padding(.horizontal)
                    }
                }
                .background(.regularMaterial)
                .cornerRadius(8)
                    .frame(height: 200)
                    .transition(.move(edge: .bottom))
            }
        }
        .onAppear {
            if showDiagnostics {
                diagnostics.startRealtimeMonitoring()
            }
        }
        .onDisappear {
            diagnostics.stopRealtimeMonitoring()
        }
    }
    
    // MARK: - Computed Properties
    
    private var filteredDevices: [AudioDevice] {
        if searchText.isEmpty {
            return deviceManager.devices
        } else {
            return deviceManager.devices.filter { device in
                // Basic name and manufacturer search
                device.name.localizedCaseInsensitiveContains(searchText) ||
                device.manufacturer.localizedCaseInsensitiveContains(searchText) ||
                
                // Device type and capabilities search
                device.deviceType.rawValue.localizedCaseInsensitiveContains(searchText) ||
                device.deviceType.accessibilityDescription.localizedCaseInsensitiveContains(searchText) ||
                
                // Channel configuration search
                device.channelDescription.localizedCaseInsensitiveContains(searchText) ||
                
                // Sample rate search
                device.sampleRateDescription.localizedCaseInsensitiveContains(searchText) ||
                
                // Special capability searches
                (searchText.localizedCaseInsensitiveContains("input") && device.canRecord) ||
                (searchText.localizedCaseInsensitiveContains("output") && device.canPlayback) ||
                (searchText.localizedCaseInsensitiveContains("bidirectional") && device.isBidirectional) ||
                (searchText.localizedCaseInsensitiveContains("virtual") && device.isVirtual) ||
                (searchText.localizedCaseInsensitiveContains("aggregate") && device.isAggregate) ||
                (searchText.localizedCaseInsensitiveContains("stereo") && (device.inputChannels == 2 || device.outputChannels == 2)) ||
                (searchText.localizedCaseInsensitiveContains("mono") && (device.inputChannels == 1 || device.outputChannels == 1)) ||
                (searchText.localizedCaseInsensitiveContains("multichannel") && (device.inputChannels > 2 || device.outputChannels > 2))
            }
        }
    }
    
    // MARK: - Diagnostic Helper Methods
    
    private func conflictsForDevice(_ device: AudioDevice) -> [SystemConflict] {
        // Get conflicts from both real-time monitoring and HAL manager
        var deviceConflicts: [SystemConflict] = []
        
        // Real-time conflicts from diagnostics
        let realtimeConflicts = diagnostics.realtimeConflicts.filter { conflict in
            conflict.affectedDevices.contains(device.deviceID)
        }
        deviceConflicts.append(contentsOf: realtimeConflicts)
        
        // Direct HAL conflicts for this specific device
        let halConflicts = CoreAudioHALManager.shared.detectDeviceSpecificConflicts(deviceID: device.deviceID)
        deviceConflicts.append(contentsOf: halConflicts)
        
        return deviceConflicts
    }
    
    @MainActor
    private func generateDiagnosticReport() async {
        logger.info("Generating diagnostic report from UI")
        let report = await diagnostics.generateFullDiagnosticReport()
        
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.plainText]
        panel.nameFieldStringValue = "WavyAudio-Diagnostic-Report-\(Date().formatted(.iso8601.year().month().day())).txt"
        panel.title = "Export Diagnostic Report"
        
        if panel.runModal() == .OK, let url = panel.url {
            do {
                try await diagnostics.exportDiagnosticReport(report, to: url)
                logger.info("Diagnostic report exported successfully")
            } catch {
                logger.error("Failed to export diagnostic report: \(error)")
            }
        }
    }
    
    @MainActor
    private func resetAudioSystem() {
        logger.info("Resetting audio system from UI")
        
        let alert = NSAlert()
        alert.messageText = "Reset Audio System"
        alert.informativeText = "This will restart the Core Audio daemon and may briefly interrupt audio playback. Continue?"
        alert.addButton(withTitle: "Reset")
        alert.addButton(withTitle: "Cancel")
        alert.alertStyle = .warning
        
        if alert.runModal() == .alertFirstButtonReturn {
            Task {
                do {
                    let process = Process()
                    process.launchPath = "/usr/bin/sudo"
                    process.arguments = ["killall", "coreaudiod"]
                    try process.run()
                    process.waitUntilExit()
                    
                    logger.info("Audio system reset completed")
                } catch {
                    logger.error("Failed to reset audio system: \(error)")
                }
            }
        }
    }

// MARK: - Preview

// Preview temporarily disabled due to circular reference
// #if DEBUG
// #Preview {
//     @Previewable @State var selectedDevice: AudioDevice? = nil
//     DeviceListSidebar(selectedDevice: $selectedDevice, deviceManager: DeviceManager())
// }
// #endif
}
