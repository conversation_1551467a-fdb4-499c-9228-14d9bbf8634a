import SwiftUI
import AVFoundation
import CoreAudio

struct PerformanceMonitorView: View {
    @Environment(AudioDiagnostics.self) private var diagnostics
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance Monitor")
                .font(.headline)
                .padding(.bottom, 4)
            
            // CPU and Memory
            HStack(spacing: 16) {
                PerformanceGauge(
                    title: "CPU",
                    value: diagnostics.cpuUsage * 100,
                    unit: "%",
                    warningThreshold: 70,
                    criticalThreshold: 90
                )
                
                PerformanceGauge(
                    title: "Memory",
                    value: Double(diagnostics.memoryUsage) / 1_000_000, // Convert to MB
                    unit: "MB",
                    warningThreshold: 500,
                    criticalThreshold: 800
                )
            }
            .frame(height: 100)
            
            // Audio Metrics
            VStack(alignment: .leading, spacing: 8) {
                MetricRow(
                    label: "Audio Latency",
                    value: String(format: "%.1f", diagnostics.audioLatency * 1000),
                    unit: "ms",
                    icon: "timer",
                    isCritical: diagnostics.audioLatency > 0.01 // 10ms
                )
                
                MetricRow(
                    label: "Buffer Size",
                    value: "\(diagnostics.currentBufferSize)",
                    unit: "frames",
                    icon: "waveform.path.ecg"
                )
                
                MetricRow(
                    label: "Buffer Underruns",
                    value: "\(diagnostics.bufferUnderruns)",
                    unit: "",
                    icon: "exclamationmark.octagon",
                    isWarning: diagnostics.bufferUnderruns > 0
                )
            }
            
            // System Status
            HStack(spacing: 16) {
                StatusBadge(
                    title: "Thermal State",
                    status: diagnostics.thermalState.status,
                    icon: "thermometer"
                )
                
                StatusBadge(
                    title: "Power Mode",
                    status: diagnostics.isLowPowerModeEnabled ? .warning : .good,
                    icon: diagnostics.isLowPowerModeEnabled ? "battery.25" : "battery.100"
                )
            }
            .padding(.top, 8)
        }
        .padding()
        .background(Color(NSColor.windowBackgroundColor))
        .cornerRadius(8)
        .shadow(radius: 2)
    }
}

// MARK: - Supporting Views

private struct PerformanceGauge: View {
    let title: String
    let value: Double
    let unit: String
    let warningThreshold: Double
    let criticalThreshold: Double
    
    private var normalizedValue: Double {
        min(max(value, 0), criticalThreshold * 1.2) / (criticalThreshold * 1.2)
    }
    
    private var statusColor: Color {
        if value >= criticalThreshold {
            return .red
        } else if value >= warningThreshold {
            return .orange
        } else {
            return .green
        }
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Gauge(value: normalizedValue, in: 0...1) {
                Text("\(Int(value))\(unit)")
                    .font(.title3.monospacedDigit())
                    .foregroundColor(.primary)
            } currentValueLabel: {
                Text("\(Int(value))\(unit)")
                    .font(.caption)
                    .foregroundColor(statusColor)
            }
            .gaugeStyle(.accessoryCircularCapacity)
            .tint(statusColor.gradient)
            .frame(width: 80, height: 80)
        }
    }
}

private struct MetricRow: View {
    let label: String
    let value: String
    let unit: String
    let icon: String
    var isWarning: Bool = false
    var isCritical: Bool = false
    
    var statusColor: Color {
        if isCritical {
            return .red
        } else if isWarning {
            return .orange
        } else {
            return .primary
        }
    }
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(statusColor)
                .frame(width: 20)
            
            Text(label)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.body.monospacedDigit())
                .foregroundColor(statusColor)
            
            if !unit.isEmpty {
                Text(unit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

private struct StatusBadge: View {
    enum Status {
        case good, warning, critical
        
        var color: Color {
            switch self {
            case .good: return .green
            case .warning: return .orange
            case .critical: return .red
            }
        }
        
        var description: String {
            switch self {
            case .good: return "Good"
            case .warning: return "Warning"
            case .critical: return "Critical"
            }
        }
    }
    
    let title: String
    let status: Status
    let icon: String
    
    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .foregroundColor(status.color)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Text(status.description)
                    .font(.caption)
                    .foregroundColor(status.color)
            }
        }
        .padding(6)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(status.color.opacity(0.1))
        .cornerRadius(4)
    }
}

// MARK: - Extensions

private extension ProcessInfo.ThermalState {
    var status: StatusBadge.Status {
        switch self {
        case .nominal: return .good
        case .fair: return .good
        case .serious: return .warning
        case .critical: return .critical
        @unknown default: return .warning
        }
    }
}

// MARK: - Preview

#Preview {
    let diagnostics = AudioDiagnostics()
    PerformanceMonitorView()
        .environment(diagnostics)
        .frame(width: 300)
        .padding()
}
