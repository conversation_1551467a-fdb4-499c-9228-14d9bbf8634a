import SwiftUI
import AVFoundation

struct DeviceDetailView: View {
    let device: AudioDevice
    @State private var volume: Float = 0.8
    @State private var isMuted = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Device Header
            HStack(alignment: .center, spacing: 16) {
                Image(systemName: device.type == .input ? "mic" : "speaker.wave.2")
                    .font(.system(size: 24))
                    .foregroundColor(.accentColor)
                    .frame(width: 48, height: 48)
                    .background(Color.accentColor.opacity(0.1))
                    .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(device.name)
                        .font(.title2)
                        .fontWeight(.medium)
                    
                    Text(device.manufacturer)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Mute toggle
                Button(action: { isMuted.toggle() }) {
                    Image(systemName: isMuted ? "speaker.slash.fill" : "speaker.wave.2.fill")
                        .font(.title3)
                        .foregroundColor(isMuted ? .red : .primary)
                        .frame(width: 36, height: 36)
                        .background(isMuted ? Color.red.opacity(0.1) : Color.gray.opacity(0.1))
                        .clipShape(Circle())
                }
                .buttonStyle(.plain)
            }
            .padding()
            
            Divider()
            
            // Volume Control
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Volume")
                        .font(.headline)
                    
                    Spacer()
                    
                    Text("\(Int(volume * 100))%")
                        .font(.body.monospacedDigit())
                        .foregroundColor(.secondary)
                        .frame(width: 50, alignment: .trailing)
                }
                
                HStack(spacing: 12) {
                    Image(systemName: "speaker.fill")
                        .foregroundColor(.secondary)
                        .frame(width: 16)
                    
                    Slider(value: $volume, in: 0...1) {
                        Text("Volume")
                    } onEditingChanged: { editing in
                        // Handle volume change
                        if !editing {
                            // Final value committed
                            updateDeviceVolume()
                        }
                    }
                    
                    Image(systemName: "speaker.wave.3.fill")
                        .foregroundColor(.secondary)
                        .frame(width: 16)
                }
            }
            .padding(.horizontal)
            
            // Audio Format
            VStack(alignment: .leading, spacing: 8) {
                Text("Audio Format")
                    .font(.headline)
                
                HStack {
                    Text("Sample Rate:")
                        .foregroundColor(.secondary)
                    Text("48.0 kHz")
                    
                    Spacer()
                    
                    Text("Bit Depth:")
                        .foregroundColor(.secondary)
                    Text("24-bit")
                }
                .font(.subheadline)
                .padding(.vertical, 4)
                
                HStack {
                    Text("Channels:")
                        .foregroundColor(.secondary)
                    Text("Stereo")
                }
                .font(.subheadline)
                .padding(.vertical, 4)
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(8)
            .padding(.horizontal)
            
            Spacer()
        }
        .padding(.top)
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
    }
    
    private func updateDeviceVolume() {
        // Update the device volume through the audio engine
        // This would call into the AudioEngineManager to update the actual device volume
        print("Setting volume to \(volume) for device: \(device.name)")
    }
}

// MARK: - Preview

#Preview {
    let device = AudioDevice(
        id: UUID(),
        deviceID: 1,
        name: "Built-in Output",
        manufacturer: "Apple Inc.",
        inputChannels: 0,
        outputChannels: 2,
        sampleRate: 44100.0,
        isVirtual: false,
        isAggregate: false,
        position: CGPoint(x: 100, y: 100)
    )
    
    DeviceDetailView(device: device)
        .frame(width: 400, height: 400)
        .padding()
}
