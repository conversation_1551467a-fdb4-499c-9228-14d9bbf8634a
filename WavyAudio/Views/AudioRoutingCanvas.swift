//
// AudioRoutingCanvas.swift - Node-Based Audio Routing Interface
// WWDC Compliance Reference:
// - WWDC19-608: Metal for Pro Apps (GPU acceleration, real-time rendering)
// - WWDC25-319: SwiftUI essentials (Canvas API, gesture handling)
// - WWDC25-268: Swift 6 concurrency (MainActor isolation for UI)
// - WWDC21-10036: Sound Analysis (Real-time audio visualization)
//
// Status: MISSING TYPES - AudioNode, NodeDragState undefined (compilation errors)
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2019-608
//

import SwiftUI
import Metal
import AppKit
import AVFoundation
import Combine
import OSLog

struct AudioRoutingCanvas: View {
    let selectedDevice: AudioDevice?
    let devices: [AudioDevice]
    let connections: [AudioConnection]
    
    @AppStorage("audioNodes") private var nodesData: Data?
    @State private var nodes: [AudioNode] = []
    
    @State private var dragState = NodeDragState()
    @State private var zoomScale: CGFloat = 1.0
    @State private var panOffset: CGSize = .zero
    
    @StateObject private var metalViewModel = MetalViewModel()
    @State private var noiseTexture: Path?
    
    // Real-time audio monitoring - Swift 6 @Observable pattern
    @State private var audioFlowMonitor = AudioFlowMonitor()
    @State private var particleSystem = AudioParticleSystem()
    @State private var flowSubscription: AnyCancellable?
    
    // WWDC Accessibility Compliance: Reduced Motion Support
    @Environment(\.accessibilityReduceMotion) private var reduceMotion
    @Environment(\.accessibilityDifferentiateWithoutColor) private var differentiateWithoutColor
    @Environment(\.accessibilityInvertColors) private var invertColors
    
    // WWDC Performance Optimization: Enhanced Animation System
    @State private var dirtyRegions: Set<CGRect> = []
    @State private var lastUpdateTime: TimeInterval = 0
    @State private var frameRateMonitor = FrameRateMonitor()
    @State private var isPerformanceOptimized = true
    
    // Spring Animation System
    @State private var springConfig = SpringAnimationConfig()
    @State private var lodLevel: LODLevel = .high
    @State private var viewportBounds: CGRect = .zero
    
    // Logging
    private let logger = Logger(subsystem: "com.wavyaudio.ui", category: "AudioRoutingCanvas")
    
    var body: some View {
        if #available(macOS 13.0, *) {
            modernCanvasView
        } else {
            fallbackView
        }
    }
    
    @available(macOS 13.0, *)
    private var modernCanvasView: some View {
        timelineCanvasView
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(.ultraThinMaterial)
            .onAppear {
                initializeNodes()
                generateNoiseTexture()
                setupAudioMonitoring()
            }
            .onDisappear {
                cleanupAudioMonitoring()
            }
            .onChange(of: devices) { _, newDevices in
                updateNodes(from: newDevices)
            }
            .onChange(of: nodes) { _, newNodes in
                saveNodes(newNodes)
            }
    }
    
    @available(macOS 13.0, *)
    private var timelineCanvasView: some View {
        TimelineView(.animation(
            minimumInterval: reduceMotion ? 1.0 / 30.0 : (1.0 / frameRateMonitor.adaptiveFrameRate), 
            paused: reduceMotion
        )) { timeline in
            canvasGeometryReader(timeline: timeline)
        }
    }
    
    @available(macOS 13.0, *)
    private func canvasGeometryReader(timeline: TimelineViewDefaultContext) -> some View {
        GeometryReader { geometry in
            canvasZStack(geometry: geometry, timeline: timeline)
        }
    }
    
    @available(macOS 13.0, *)
    private func canvasZStack(geometry: GeometryProxy, timeline: TimelineViewDefaultContext) -> some View {
        ZStack {
            metalBackgroundView
            canvasContent(geometry: geometry, timeline: timeline)
            overlayControls(geometrySize: geometry.size)
        }
    }
    
    private var fallbackView: some View {
        Text("This view requires macOS 13.0 or later.")
    }
    
    // MARK: - Spring Animation System
    
    struct SpringAnimationConfig {
        let response: Double = 0.5
        let dampingFraction: Double = 0.8
        let blendDuration: Double = 0.3
        
        var springAnimation: Animation {
            .spring(response: response, dampingFraction: dampingFraction, blendDuration: blendDuration)
        }
    }
    
    // MARK: - Level of Detail System
    
    enum LODLevel: String, CaseIterable {
        case low = "Low"
        case medium = "Medium" 
        case high = "High"
        
        var connectionLineWidth: CGFloat {
            switch self {
            case .low: return 1.0
            case .medium: return 2.0
            case .high: return 3.0
            }
        }
        
        var showAnimations: Bool {
            switch self {
            case .low: return false
            case .medium: return true
            case .high: return true
            }
        }
        
        var showComplexEffects: Bool {
            switch self {
            case .low: return false
            case .medium: return false
            case .high: return true
            }
        }
    }

    private var metalBackgroundView: some View {
        MetalBackgroundView(metalDevice: metalViewModel.metalDevice, noiseTexture: noiseTexture)
            .drawingGroup() // Enable Metal rendering
    }
    
    @MainActor @ViewBuilder 
    private func canvasContent(geometry: GeometryProxy, timeline: TimelineViewDefaultContext) -> some View {
        logger.info("Entering canvasContent")
        Canvas { context, size in
            drawCanvas(context: context, size: size, timeline: timeline)
        }
        .drawingGroup() // Metal optimization
        .scaleEffect(zoomScale)
        .offset(panOffset)
        .gesture(canvasGestures)
        .clipped()
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Audio Routing Canvas")
        .accessibilityValue("Zoom: \(String(format: "%.0f", zoomScale * 100))%, \(devices.count) devices, \(connections.count) connections")
        .accessibilityHint("Double tap to reset view, pinch to zoom, drag to pan")
        .accessibilityActions {
            Button("Reset View") {
                if reduceMotion {
                    zoomScale = 1.0
                    panOffset = .zero
                } else {
                    withAnimation(springConfig.springAnimation) {
                        zoomScale = 1.0
                        panOffset = .zero
                    }
                }
            }
            Button("Auto Layout") {
                autoLayoutNodes()
            }
        }
    }

    private func overlayControls(geometrySize: CGSize) -> some View {
        VStack {
            Spacer()
            HStack {
                canvasControls(geometrySize: geometrySize)
                Spacer()
                connectionInfo
            }
            .padding()
        }
    }
    
    // MARK: - Canvas Drawing
    
    private func drawCanvas(context: GraphicsContext, size: CGSize, timeline: TimelineViewDefaultContext) {
        // WWDC Performance: Record frame start for monitoring
        frameRateMonitor.recordFrameStart()
        
        // Update viewport bounds for culling
        viewportBounds = CGRect(origin: CGPoint(x: -panOffset.width / zoomScale, y: -panOffset.height / zoomScale),
                               size: CGSize(width: size.width / zoomScale, height: size.height / zoomScale))
        
        // Dynamic LOD calculation based on zoom and performance
        lodLevel = calculateLODLevel(zoomScale: zoomScale, performance: frameRateMonitor.averageFPS)
        
        let currentTime = timeline.date.timeIntervalSinceReferenceDate
        let _ = currentTime - lastUpdateTime // timeDelta for future animation use
        lastUpdateTime = currentTime
        
        // Performance optimization: Skip expensive drawing if frame rate is low
        let shouldSkipComplexDrawing = frameRateMonitor.shouldOptimize && !reduceMotion
        
        // Draw grid background (always visible)
        drawGrid(context: context, size: size)
        
        // Draw connections with viewport culling and LOD
        let visibleConnectionsList = visibleConnections()
        
        // Batch update animations for better performance
        if !shouldSkipComplexDrawing && lodLevel.showAnimations {
            var mutableConnections = Array(visibleConnectionsList)
            AudioConnection.updateAnimationsBatch(&mutableConnections, deltaTime: currentTime - lastUpdateTime)
            
            for connection in mutableConnections {
                drawConnection(context: context, connection: connection, optimized: shouldSkipComplexDrawing)
            }
        } else {
            for connection in visibleConnectionsList {
                drawConnection(context: context, connection: connection, optimized: shouldSkipComplexDrawing)
            }
        }
        
        // Draw device nodes with viewport culling
        let visibleNodesList = visibleNodes()
        for node in visibleNodesList {
            drawDeviceNode(context: context, node: node, optimized: shouldSkipComplexDrawing)
            
            // Draw real-time volume meters for each device
            if !shouldSkipComplexDrawing {
                drawRealtimeVolumeMeter(context: context, node: node)
            }
        }
        
        // Draw real-time particle system
        if !shouldSkipComplexDrawing {
            particleSystem.renderParticles(context: context, optimized: frameRateMonitor.shouldOptimize)
        }
        
        // Draw selection indicators
        drawSelectionIndicators(context: context)
        
        // Draw drag preview if active
        if dragState.isActive {
            drawDragPreview(context: context)
        }
        
        // Clear dirty regions after drawing
        if !dirtyRegions.isEmpty {
            dirtyRegions.removeAll()
        }
    }
    
    private func drawGrid(context: GraphicsContext, size: CGSize) {
        let spacing: CGFloat = 40 * zoomScale
        let gridColor = Color.secondary.opacity(0.2)
        
        var path = Path()
        
        // Vertical lines
        var x: CGFloat = spacing
        while x < size.width {
            path.move(to: CGPoint(x: x, y: 0))
            path.addLine(to: CGPoint(x: x, y: size.height))
            x += spacing
        }
        
        // Horizontal lines
        var y: CGFloat = spacing
        while y < size.height {
            path.move(to: CGPoint(x: 0, y: y))
            path.addLine(to: CGPoint(x: size.width, y: y))
            y += spacing
        }
        
        context.stroke(path, with: .color(gridColor), lineWidth: 1)
    }
    
    private func drawConnection(context: GraphicsContext, connection: AudioConnection, optimized: Bool = false) {
        guard let startNode = nodes.first(where: { $0.deviceID == connection.sourceDeviceID }),
              let endNode = nodes.first(where: { $0.deviceID == connection.destinationDeviceID }) else {
            return
        }
        
        let path = createConnectionPath(from: startNode.position, to: endNode.position)
        let connectionType = connection.connectionType
        
        // Apply animation effects (simplified if optimized)
        let animatedColor: Color
        let animatedWidth: CGFloat
        
        if optimized || !lodLevel.showComplexEffects {
            // Static appearance when optimizing or using low LOD
            animatedColor = connectionType.color.opacity(0.7)
            animatedWidth = lodLevel.connectionLineWidth
        } else {
            // Full animation when performance allows
            animatedColor = connectionType.color.opacity(0.7 + 0.3 * connection.pulseIntensity)
            animatedWidth = lodLevel.connectionLineWidth * (1.0 + 0.2 * connection.pulseIntensity)
        }
        
        // Draw connection line
        context.stroke(
            path,
            with: .color(animatedColor),
            style: StrokeStyle(
                lineWidth: animatedWidth,
                lineCap: .round,
                lineJoin: .round,
                dash: optimized ? [] : connectionType.pattern
            )
        )
        
        // Draw signal flow animation with real-time data (skip if optimized)
        if !optimized && connection.isActive {
            let realtimeSignalStrength = audioFlowMonitor.getSignalStrength(for: connection)
            if realtimeSignalStrength > 0.01 {
                drawRealtimeSignalFlow(context: context, path: path, connection: connection, signalStrength: realtimeSignalStrength)
            }
        }
        
        // Draw connection endpoints
        drawConnectionEndpoints(context: context, start: startNode.position, end: endNode.position)
    }
    
    private func createConnectionPath(from start: CGPoint, to end: CGPoint) -> Path {
        var path = Path()
        path.move(to: start)
        
        // Create curved connection
        let distance = sqrt(pow(end.x - start.x, 2) + pow(end.y - start.y, 2))
        let controlOffset = min(distance * 0.3, 100)
        
        let midX = (start.x + end.x) / 2
        let midY = (start.y + end.y) / 2
        
        // Calculate perpendicular offset for curve
        let angle = atan2(end.y - start.y, end.x - start.x)
        let perpAngle = angle + .pi / 2
        
        let controlPoint = CGPoint(
            x: midX + cos(perpAngle) * controlOffset,
            y: midY + sin(perpAngle) * controlOffset
        )
        
        path.addQuadCurve(to: end, control: controlPoint)
        return path
    }
    
    private func drawRealtimeSignalFlow(context: GraphicsContext, path: Path, connection: AudioConnection, signalStrength: Float) {
        // Draw real-time signal flow based on actual audio levels
        if reduceMotion {
            // Static signal strength indicator
            let staticDotSize: CGFloat = 4 + CGFloat(signalStrength * 8)
            let midPoint = path.trimmedPath(from: 0, to: 0.5).currentPoint ?? CGPoint.zero
            
            let staticDotPath = Path(ellipseIn: CGRect(
                x: midPoint.x - staticDotSize / 2,
                y: midPoint.y - staticDotSize / 2,
                width: staticDotSize,
                height: staticDotSize
            ))
            
            let opacity = differentiateWithoutColor ? 1.0 : (0.8 * Double(signalStrength))
            
            // Use screen blend mode for additive lighting
            var signalContext = context
            signalContext.blendMode = .screen
            signalContext.opacity = opacity
            signalContext.fill(staticDotPath, with: .color(connection.connectionType.color))
            return
        }
        
        // Dynamic particle count based on signal strength
        let particleCount = max(1, Int(signalStrength * 20))
        let dotSize: CGFloat = 3 + CGFloat(signalStrength * 5)
        let flowSpeed = Double(signalStrength) * 3.0 // Faster flow for stronger signals
        
        // Use screen blend mode for additive lighting effects
        var signalContext = context
        signalContext.blendMode = .screen
        
        for i in 0..<particleCount {
            let baseProgress = Double(i) / Double(particleCount)
            let timeOffset = Date().timeIntervalSinceReferenceDate * flowSpeed
            let progress = (baseProgress + timeOffset * 0.5).truncatingRemainder(dividingBy: 1.0)
            
            if let point = path.trimmedPath(from: 0, to: progress).currentPoint {
                let dotPath = Path(ellipseIn: CGRect(
                    x: point.x - dotSize / 2,
                    y: point.y - dotSize / 2,
                    width: dotSize,
                    height: dotSize
                ))
                
                // Progressive opacity for trail effect
                let trailOpacity = 1.0 - Double(i) / Double(particleCount) * 0.7
                let finalOpacity = differentiateWithoutColor ? trailOpacity : (trailOpacity * Double(signalStrength))
                
                signalContext.opacity = finalOpacity
                signalContext.fill(dotPath, with: .color(connection.connectionType.color))
            }
        }
    }
    
    private func drawSignalFlow(context: GraphicsContext, path: Path, connection: AudioConnection) {
        // Draw animated dots flowing along the connection
        // WWDC Accessibility: Skip animations if reduced motion is enabled
        if reduceMotion {
            // Static signal strength indicator
            let staticDotSize: CGFloat = 6
            let midPoint = path.trimmedPath(from: 0, to: 0.5).currentPoint ?? CGPoint.zero
            
            let staticDotPath = Path(ellipseIn: CGRect(
                x: midPoint.x - staticDotSize / 2,
                y: midPoint.y - staticDotSize / 2,
                width: staticDotSize,
                height: staticDotSize
            ))
            
            let opacity = differentiateWithoutColor ? 1.0 : (0.8 * connection.signalStrength)
            context.fill(staticDotPath, with: .color(.white.opacity(opacity)))
            return
        }
        
        let dotCount = 5
        let dotSize: CGFloat = 4
        
        for i in 0..<dotCount {
            let progress = (Double(i) / Double(dotCount) + connection.animationPhase) * 0.5
            let normalizedProgress = progress.truncatingRemainder(dividingBy: 1.0)
            
            if let point = path.trimmedPath(from: 0, to: normalizedProgress).currentPoint {
                let dotPath = Path(ellipseIn: CGRect(
                    x: point.x - dotSize / 2,
                    y: point.y - dotSize / 2,
                    width: dotSize,
                    height: dotSize
                ))
                
                let opacity = differentiateWithoutColor ? 1.0 : (0.8 * connection.signalStrength)
                context.fill(dotPath, with: .color(.white.opacity(opacity)))
            }
        }
    }
    
    private func drawConnectionEndpoints(context: GraphicsContext, start: CGPoint, end: CGPoint) {
        let endpointSize: CGFloat = 6
        
        // Start point (output)
        let startPath = Path(ellipseIn: CGRect(
            x: start.x - endpointSize / 2,
            y: start.y - endpointSize / 2,
            width: endpointSize,
            height: endpointSize
        ))
        context.fill(startPath, with: .color(.green))
        
        // End point (input)
        let endPath = Path(ellipseIn: CGRect(
            x: end.x - endpointSize / 2,
            y: end.y - endpointSize / 2,
            width: endpointSize,
            height: endpointSize
        ))
        context.fill(endPath, with: .color(.red))
    }
    
    private func drawDeviceNode(context: GraphicsContext, node: AudioNode, optimized: Bool = false) {
        let nodeSize = node.nodeType.size
        let nodeRect = CGRect(
            x: node.position.x - nodeSize.width / 2,
            y: node.position.y - nodeSize.height / 2,
            width: nodeSize.width,
            height: nodeSize.height
        )
        
        // Node background
        let backgroundColor = node.isSelected ? node.nodeType.color.opacity(0.8) : node.nodeType.color.opacity(0.6)
        let backgroundPath = Path(roundedRect: nodeRect, cornerRadius: 8)
        context.fill(backgroundPath, with: .color(backgroundColor))
        
        // Node border
        let borderColor = node.isSelected ? Color.white : Color.black.opacity(0.3)
        context.stroke(backgroundPath, with: .color(borderColor), lineWidth: node.isSelected ? 2 : 1)
        
        // Device name label
        let textRect = CGRect(
            x: nodeRect.minX + 8,
            y: nodeRect.midY - 10,
            width: nodeRect.width - 16,
            height: 20
        )
        
        context.draw(
            Text(node.deviceName)
                .font(.caption)
                .foregroundColor(.white),
            in: textRect
        )
        
        // Connection ports (skip if optimized for performance)
        if !optimized {
            drawConnectionPorts(context: context, node: node, nodeRect: nodeRect)
        }
    }
    
    private func drawConnectionPorts(context: GraphicsContext, node: AudioNode, nodeRect: CGRect) {
        let portSize: CGFloat = 8
        let portSpacing: CGFloat = 16
        
        // Input ports (left side)
        let inputCount = node.inputChannels
        for i in 0..<inputCount {
            let y = nodeRect.midY + CGFloat(i - inputCount / 2) * portSpacing
            let portRect = CGRect(
                x: nodeRect.minX - portSize / 2,
                y: y - portSize / 2,
                width: portSize,
                height: portSize
            )
            
            let portPath = Path(ellipseIn: portRect)
            context.fill(portPath, with: .color(.red))
            context.stroke(portPath, with: .color(.white), lineWidth: 1)
        }
        
        // Output ports (right side)
        let outputCount = node.outputChannels
        for i in 0..<outputCount {
            let y = nodeRect.midY + CGFloat(i - outputCount / 2) * portSpacing
            let portRect = CGRect(
                x: nodeRect.maxX - portSize / 2,
                y: y - portSize / 2,
                width: portSize,
                height: portSize
            )
            
            let portPath = Path(ellipseIn: portRect)
            context.fill(portPath, with: .color(.green))
            context.stroke(portPath, with: .color(.white), lineWidth: 1)
        }
    }
    
    private func drawSelectionIndicators(context: GraphicsContext) {
        if let selectedDevice = selectedDevice,
           let selectedNode = nodes.first(where: { $0.deviceID == selectedDevice.deviceID }) {
            
            let nodeSize = selectedNode.nodeType.size
            let selectionRect = CGRect(
                x: selectedNode.position.x - nodeSize.width / 2 - 4,
                y: selectedNode.position.y - nodeSize.height / 2 - 4,
                width: nodeSize.width + 8,
                height: nodeSize.height + 8
            )
            
            let selectionPath = Path(roundedRect: selectionRect, cornerRadius: 12)
            context.stroke(selectionPath, with: .color(.accentColor), lineWidth: 3)
        }
    }
    
    private func drawDragPreview(context: GraphicsContext) {
        // Draw preview connection line during drag
        if let dragStart = dragState.dragStart,
           let currentPosition = dragState.currentPosition {
            
            let previewPath = createConnectionPath(from: dragStart, to: currentPosition)
            context.stroke(
                previewPath,
                with: .color(.white.opacity(0.5)),
                style: StrokeStyle(lineWidth: 2, dash: [5, 5])
            )
        }
    }
    
    // MARK: - Gestures
    
    private var canvasGestures: some Gesture {
        SimultaneousGesture(
            // Pan gesture with animation interruption handling
            DragGesture()
                .onChanged { value in
                    if !dragState.isActive {
                        // WWDC Performance: Interrupt any ongoing animations
                        NSAnimationContext.current.allowsImplicitAnimation = false
                        
                        panOffset = CGSize(
                            width: panOffset.width + value.translation.width,
                            height: panOffset.height + value.translation.height
                        )
                    }
                },
            
            // Zoom gesture with performance optimization
            MagnificationGesture()
                .onChanged { scale in
                    // WWDC Performance: Interrupt animations and optimize zoom
                    NSAnimationContext.current.allowsImplicitAnimation = false
                    zoomScale = max(0.5, min(3.0, scale))
                    
                    // Mark viewport as dirty for optimization
                    addDirtyRegion(CGRect(x: 0, y: 0, width: 2048, height: 2048))
                }
        )
    }
    
    // MARK: - Controls
    
    private func canvasControls(geometrySize: CGSize) -> some View {
        CanvasControls(
            geometrySize: geometrySize,
            zoomScale: $zoomScale,
            panOffset: $panOffset,
            onResetView: {
                if reduceMotion {
                    zoomScale = 1.0
                    panOffset = .zero
                } else {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        zoomScale = 1.0
                        panOffset = .zero
                    }
                }
            },
            onFitToScreen: { fitToScreen(geometrySize: geometrySize) },
            onAutoLayout: autoLayoutNodes
        )
    }
    
    // MARK: - Controls
    
    private var connectionInfo: some View {
        ConnectionInfoView(
            deviceCount: devices.count,
            connectionCount: connections.count,
            zoomScale: zoomScale
        )
    }
    
    // MARK: - Node Management
    
    private func initializeNodes() {
        if let data = nodesData,
           let decodedNodes = try? JSONDecoder().decode([AudioNode].self, from: data) {
            nodes = decodedNodes
        } else {
            nodes = devices.enumerated().map { index, device in
                AudioNode(from: device)
            }
            autoLayoutNodes()
        }
    }
    
    private func saveNodes(_ nodes: [AudioNode]) {
        if let data = try? JSONEncoder().encode(nodes) {
            nodesData = data
        }
    }
    
    private func updateNodes(from devices: [AudioDevice]) {
        // Update existing nodes and add new ones
        var newNodes: [AudioNode] = []
        
        for device in devices {
            if let existingNode = nodes.first(where: { $0.deviceID == device.deviceID }) {
                newNodes.append(existingNode)
            } else {
                let newNode = AudioNode(from: device)
                newNodes.append(newNode)
            }
        }
        
        nodes = newNodes
        autoLayoutNodes()
    }
    
    private func autoLayoutNodes() {
        guard !nodes.isEmpty else { return }
        
        // Simple grid layout
        let columns = Int(ceil(sqrt(Double(nodes.count))))
        let spacing: CGFloat = 200
        let startX: CGFloat = 150
        let startY: CGFloat = 150
        
        for (index, _) in nodes.enumerated() {
            let column = index % columns
            let row = index / columns
            
            let position = CGPoint(
                x: startX + CGFloat(column) * spacing,
                y: startY + CGFloat(row) * spacing
            )
            
            nodes[index].position = position
        }
    }
    
    // MARK: - Dirty Region Tracking
    
    private func addDirtyRegion(_ rect: CGRect) {
        dirtyRegions.insert(rect)
    }
    
    private func addDirtyRegionForNode(_ node: AudioNode) {
        let nodeSize = node.nodeType.size
        let dirtyRect = CGRect(
            x: node.position.x - nodeSize.width / 2 - 10,
            y: node.position.y - nodeSize.height / 2 - 10,
            width: nodeSize.width + 20,
            height: nodeSize.height + 20
        )
        addDirtyRegion(dirtyRect)
    }
    
    private func addDirtyRegionForConnection(from start: CGPoint, to end: CGPoint) {
        let minX = min(start.x, end.x) - 20
        let maxX = max(start.x, end.x) + 20
        let minY = min(start.y, end.y) - 20
        let maxY = max(start.y, end.y) + 20
        
        let dirtyRect = CGRect(
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY
        )
        addDirtyRegion(dirtyRect)
    }
    
    private func fitToScreen(geometrySize: CGSize) {
        // Calculate bounding box of all nodes
        guard !nodes.isEmpty else { return }
        
        let minX = nodes.map { $0.position.x }.min() ?? 0
        let maxX = nodes.map { $0.position.x }.max() ?? 0
        let minY = nodes.map { $0.position.y }.min() ?? 0
        let maxY = nodes.map { $0.position.y }.max() ?? 0
        
        let contentWidth = maxX - minX + 200
        let contentHeight = maxY - minY + 200
        
        // Calculate appropriate zoom level
        let scaleX = geometrySize.width / contentWidth
        let scaleY = geometrySize.height / contentHeight
        let newScale = min(scaleX, scaleY, 2.0)
        
        if reduceMotion {
            zoomScale = newScale
            panOffset = CGSize(
                width: -(minX + maxX) / 2 * newScale + geometrySize.width / 2,
                height: -(minY + maxY) / 2 * newScale + geometrySize.height / 2
            )
        } else {
            withAnimation(.easeInOut(duration: 0.5)) {
                zoomScale = newScale
                panOffset = CGSize(
                    width: -(minX + maxX) / 2 * newScale + geometrySize.width / 2,
                    height: -(minY + maxY) / 2 * newScale + geometrySize.height / 2
                )
            }
        }
    }
    
    // MARK: - Noise Texture
    
    private func generateNoiseTexture() {
        var path = Path()
        let spacing: CGFloat = 4
        
        for x in stride(from: 0, to: 2048, by: spacing) {
            for y in stride(from: 0, to: 2048, by: spacing) {
                if Double.random(in: 0...1) > 0.7 {
                    let dotPath = Path(ellipseIn: CGRect(x: x, y: y, width: 1, height: 1))
                    path.addPath(dotPath)
                }
            }
        }
        self.noiseTexture = path
    }
    
    // MARK: - LOD and Performance Methods
    
    private func calculateLODLevel(zoomScale: CGFloat, performance: Double) -> LODLevel {
        if performance < 30.0 {
            return .low
        } else if zoomScale < 0.5 || performance < 45.0 {
            return .medium
        } else {
            return .high
        }
    }
    
    private func visibleConnections() -> [AudioConnection] {
        return connections.filter { connection in
            let connectionBounds = CGRect(
                x: min(connection.startPoint.x, connection.endPoint.x) - 50,
                y: min(connection.startPoint.y, connection.endPoint.y) - 50,
                width: abs(connection.endPoint.x - connection.startPoint.x) + 100,
                height: abs(connection.endPoint.y - connection.startPoint.y) + 100
            )
            return viewportBounds.intersects(connectionBounds)
        }
    }
    
    private func visibleNodes() -> [AudioNode] {
        return nodes.filter { node in
            let nodeSize = node.nodeType.size
            let nodeBounds = CGRect(
                x: node.position.x,
                y: node.position.y,
                width: nodeSize.width,
                height: nodeSize.height
            )
            return viewportBounds.intersects(nodeBounds)
        }
    }
    
    // MARK: - Enhanced Dirty Region System
    
    private func shouldRedrawRegion(_ rect: CGRect) -> Bool {
        return dirtyRegions.contains { $0.intersects(rect) }
    }
    
    // MARK: - Audio Monitoring Setup
    
    private func setupAudioMonitoring() {
        do {
            try audioFlowMonitor.startMonitoring()
            particleSystem.startEmission()
            
            // Subscribe to real-time audio levels
            flowSubscription = audioFlowMonitor.signalLevelsPublisher
                .sink { [weak self] levels in
                    self?.updateParticleSystemWithAudioData(levels)
                }
            
            logger.info("Audio monitoring setup completed")
        } catch {
            logger.error("Failed to start audio monitoring: \(error)")
        }
    }
    
    private func cleanupAudioMonitoring() {
        audioFlowMonitor.stopMonitoring()
        particleSystem.stopEmission()
        flowSubscription?.cancel()
        flowSubscription = nil
        
        logger.info("Audio monitoring cleanup completed")
    }
    
    private func updateParticleSystemWithAudioData(_ levels: [Float]) {
        // Update particle system with real-time audio data
        let currentConnections = visibleConnections()
        particleSystem.updateParticles(connections: currentConnections, signalData: levels)
    }
    
    // MARK: - Real-time Volume Meter Drawing
    
    private func drawRealtimeVolumeMeter(context: GraphicsContext, node: AudioNode) {
        let levels = audioFlowMonitor.channelLevels
        guard !levels.isEmpty else { return }
        
        let meterWidth: CGFloat = 4
        let meterHeight: CGFloat = 40
        let channelSpacing: CGFloat = 6
        let meterPosition = CGPoint(
            x: node.position.x + node.nodeType.size.width / 2 + 10,
            y: node.position.y - meterHeight / 2
        )
        
        for (channelIndex, level) in levels.enumerated() {
            let xOffset = CGFloat(channelIndex) * channelSpacing
            let meterRect = CGRect(
                x: meterPosition.x + xOffset,
                y: meterPosition.y,
                width: meterWidth,
                height: meterHeight
            )
            
            // Background
            context.fill(Path(meterRect), with: .color(.gray.opacity(0.3)))
            
            // Level indicator
            let levelHeight = meterHeight * CGFloat(level)
            let levelRect = CGRect(
                x: meterRect.minX,
                y: meterRect.maxY - levelHeight,
                width: meterWidth,
                height: levelHeight
            )
            
            // Color based on level with accessibility support
            let levelColor: Color
            if differentiateWithoutColor {
                // Use patterns instead of just colors for accessibility
                levelColor = level > 0.8 ? .white : level > 0.6 ? .gray : .black
            } else {
                levelColor = level > 0.8 ? .red : level > 0.6 ? .orange : .green
            }
            
            context.fill(Path(levelRect), with: .color(levelColor))
            
            // Add peak indicator
            if level > 0.05 {
                let peakRect = CGRect(
                    x: meterRect.minX,
                    y: meterRect.minY + CGFloat(1.0 - level) * meterHeight - 1,
                    width: meterWidth,
                    height: 2
                )
                context.fill(Path(peakRect), with: .color(.white))
            }
        }
        
        // Add dB scale labels (simplified)
        if zoomScale > 0.8 { // Only show labels when zoomed in enough
            let dbLabels = ["0", "-6", "-12", "-18", "-24"]
            for (index, label) in dbLabels.enumerated() {
                let labelY = meterPosition.y + CGFloat(index) * (meterHeight / CGFloat(dbLabels.count - 1))
                let labelRect = CGRect(
                    x: meterPosition.x + CGFloat(levels.count) * channelSpacing + 5,
                    y: labelY - 6,
                    width: 20,
                    height: 12
                )
                
                context.draw(
                    Text(label)
                        .font(.caption2)
                        .foregroundColor(.secondary),
                    in: labelRect
                )
            }
        }
    }
}

// MARK: - Supporting Types

struct NodeDragState {
    var isActive: Bool = false
    var dragStart: CGPoint?
    var currentPosition: CGPoint?
    var selectedNodeID: UUID?
}

// MARK: - Performance Monitoring

@Observable
class FrameRateMonitor {
    private(set) var currentFPS: Double = 60.0
    private(set) var averageFPS: Double = 60.0
    private var frameStartTimes: [TimeInterval] = []
    private let maxSamples = 60
    
    func recordFrameStart() {
        let now = CACurrentMediaTime()
        frameStartTimes.append(now)
        
        // Keep only recent samples
        if frameStartTimes.count > maxSamples {
            frameStartTimes.removeFirst(frameStartTimes.count - maxSamples)
        }
        
        // Calculate FPS
        if frameStartTimes.count >= 2 {
            let timeDiff = frameStartTimes.last! - frameStartTimes.first!
            let fps = Double(frameStartTimes.count - 1) / timeDiff
            currentFPS = fps
            
            // Update average
            averageFPS = (averageFPS * 0.9) + (fps * 0.1)
        }
    }
    
    var shouldOptimize: Bool {
        averageFPS < 30.0 // Optimize if below 30 FPS
    }
    
    var targetFrameRate: Double {
        shouldOptimize ? 30.0 : 60.0
    }
    
    var adaptiveFrameRate: Double {
        // Dynamic frame rate based on performance
        if averageFPS >= 58.0 {
            return 120.0  // ProMotion support for ultra-smooth
        } else if averageFPS >= 45.0 {
            return 60.0   // Standard smooth
        } else if averageFPS >= 25.0 {
            return 30.0   // Performance fallback
        } else {
            return 15.0   // Emergency low-performance mode
        }
    }
}

// MARK: - Metal Background View

struct MetalBackgroundView: View {
    let metalDevice: MTLDevice?
    let noiseTexture: Path?
    
    var body: some View {
        Rectangle()
            .fill(.clear)
            .background {
                Canvas { context, size in
                    drawMetalBackground(context: context, size: size)
                }
                .drawingGroup() // Enable Metal rendering
            }
    }
    
    private func drawMetalBackground(context: GraphicsContext, size: CGSize) {
        // Create subtle gradient background optimized for Metal
        let gradient = Gradient(colors: [
            .black.opacity(0.1),
            .blue.opacity(0.05),
            .black.opacity(0.1)
        ])
        
        let gradientFill = GraphicsContext.Shading.radialGradient(
            gradient,
            center: CGPoint(x: size.width / 2, y: size.height / 2),
            startRadius: 0,
            endRadius: max(size.width, size.height) / 2
        )
        
        context.fill(Path(CGRect(origin: .zero, size: size)), with: gradientFill)
        
        // Add subtle noise pattern for depth
        if let noiseTexture = noiseTexture {
            context.fill(noiseTexture, with: .color(.white.opacity(0.02)))
        }
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    AudioRoutingCanvas(
        selectedDevice: nil as AudioDevice?,
        devices: [],
        connections: []
    )
}
#endif
