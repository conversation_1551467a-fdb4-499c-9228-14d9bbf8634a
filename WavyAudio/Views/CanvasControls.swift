import SwiftUI

struct CanvasControls: View {
    let geometrySize: CGSize
    @Binding var zoomScale: CGFloat
    @Binding var panOffset: CGSize
    let onResetView: () -> Void
    let onFitToScreen: () -> Void
    let onAutoLayout: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            <PERSON><PERSON>("Reset View", systemImage: "arrow.counterclockwise") {
                onResetView()
            }
            .buttonStyle(.bordered)
            .accessibilityLabel("Reset View")
            
            <PERSON><PERSON>("Zoom In", systemImage: "plus.magnifyingglass") {
                withAnimation(.easeInOut(duration: 0.2)) {
                    zoomScale = min(zoomScale * 1.2, 3.0)
                }
            }
            .buttonStyle(.bordered)
            .accessibilityLabel("Zoom In")
            
            But<PERSON>("Zoom Out", systemImage: "minus.magnifyingglass") {
                withAnimation(.easeInOut(duration: 0.2)) {
                    zoomScale = max(zoomScale / 1.2, 0.3)
                }
            }
            .buttonStyle(.bordered)
            .accessibilityLabel("Zoom Out")
            
            <PERSON><PERSON>("Fit to Screen", systemImage: "rectangle.compress.vertical") {
                onFitToScreen()
            }
            .buttonStyle(.bordered)
            .accessibilityLabel("Fit to Screen")
            
            Button("Auto Layout", systemImage: "rectangle.grid.3x2") {
                onAutoLayout()
            }
            .buttonStyle(.bordered)
            .accessibilityLabel("Auto Layout Nodes")
        }
    }
}