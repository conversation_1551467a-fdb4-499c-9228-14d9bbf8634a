import SwiftUI

struct DeviceRowView: View, Equatable {
    let device: AudioDevice
    let isSelected: Bool
    let conflicts: [SystemConflict]

    nonisolated static func == (lhs: DeviceRowView, rhs: DeviceRowView) -> Bool {
        // Equatable conformance for performance:
        // The view only needs to be redrawn if the device ID, selection state, or conflicts change.
        // This prevents re-rendering the entire view for minor property changes
        // that don't affect its visual representation here.
        lhs.device.id == rhs.device.id && 
        lhs.isSelected == rhs.isSelected &&
        lhs.conflicts.count == rhs.conflicts.count &&
        lhs.highestSeverity == rhs.highestSeverity
    }
    
    // Helper to get the highest severity for comparison
    private var highestSeverity: ConflictSeverity? {
        conflicts.max(by: { $0.severity.priority < $1.severity.priority })?.severity
    }

    var body: some View {
        HStack {
            // Primary device icon with overlay for virtual/aggregate distinction
            ZStack(alignment: .bottomTrailing) {
                Image(systemName: device.deviceType.icon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(device.deviceType.color)
                
                // Secondary status icon for virtual/aggregate devices
                if let statusIcon = device.deviceType.deviceStatusIcon {
                    Image(systemName: statusIcon)
                        .font(.system(size: 8, weight: .semibold))
                        .foregroundColor(.white)
                        .background(Circle().fill(device.deviceType.color).frame(width: 12, height: 12))
                        .offset(x: 2, y: 2)
                }
            }

            VStack(alignment: .leading) {
                Text(device.displayName)
                    .font(.headline)
                    .lineLimit(1)
                Text(device.manufacturer)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            Spacer()
            VStack(alignment: .trailing) {
                Text(device.channelDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(device.sampleRateDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Conflict severity indicators
            if !conflicts.isEmpty {
                conflictIndicators
            }
        }
        .padding(.vertical, 4)
        .background(isSelected ? Color.accentColor.opacity(0.2) : Color.clear)
        .cornerRadius(5)
        // Enhanced accessibility
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityValue(accessibilityValue)
        .accessibilityHint(accessibilityHint)
        .accessibilityActions {
            if !conflicts.isEmpty {
                Button("View Conflicts") {
                    // Could trigger a conflict detail view
                }
            }
        }
    }
    
    // MARK: - Conflict Indicators
    
    @ViewBuilder
    private var conflictIndicators: some View {
        VStack(alignment: .trailing, spacing: 2) {
            // Show count badge
            Text("\(conflicts.count)")
                .font(.caption2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(minWidth: 16, minHeight: 16)
                .background(severityColor)
                .clipShape(Circle())
            
            // Show highest severity icon
            Image(systemName: severityIcon)
                .font(.caption)
                .foregroundColor(severityColor)
        }
        .accessibilityLabel("Device conflicts")
        .accessibilityValue("\(conflicts.count) conflicts, highest severity: \(highestSeverity?.rawValue ?? "none")")
    }
    
    // MARK: - Computed Properties
    
    private var severityColor: Color {
        guard let severity = highestSeverity else { return .secondary }
        switch severity {
        case .info:
            return .blue
        case .warning:
            return .orange
        case .errorLevel:
            return .red
        case .critical:
            return .purple
        }
    }
    
    private var severityIcon: String {
        guard let severity = highestSeverity else { return "checkmark.circle" }
        switch severity {
        case .info:
            return "info.circle"
        case .warning:
            return "exclamationmark.triangle"
        case .errorLevel:
            return "xmark.circle"
        case .critical:
            return "exclamationmark.octagon"
        }
    }
    
    // MARK: - Accessibility Properties
    
    private var accessibilityLabel: String {
        let deviceTypeLabel = device.deviceType.accessibilityDescription
        let statusLabel = isSelected ? "Selected" : ""
        return "\(device.displayName) \(deviceTypeLabel) \(statusLabel)".trimmingCharacters(in: .whitespaces)
    }
    
    private var accessibilityValue: String {
        var valueComponents: [String] = []
        
        // Device details
        valueComponents.append("Manufacturer: \(device.manufacturer)")
        valueComponents.append("Channels: \(device.channelDescription)")
        valueComponents.append("Sample rate: \(device.sampleRateDescription)")
        
        // Device capabilities
        var capabilities: [String] = []
        if device.canRecord { capabilities.append("recording") }
        if device.canPlayback { capabilities.append("playback") }
        if device.isVirtual { capabilities.append("virtual") }
        if device.isAggregate { capabilities.append("aggregate") }
        
        if !capabilities.isEmpty {
            valueComponents.append("Capabilities: \(capabilities.joined(separator: ", "))")
        }
        
        // Conflict information
        if !conflicts.isEmpty {
            let severityLevel = highestSeverity?.rawValue ?? "unknown"
            valueComponents.append("Conflicts: \(conflicts.count) (\(severityLevel) severity)")
        }
        
        return valueComponents.joined(separator: ". ")
    }
    
    private var accessibilityHint: String {
        var hints: [String] = []
        
        hints.append("Tap to select this audio device")
        
        if !conflicts.isEmpty {
            hints.append("Has conflicts that may affect performance")
        }
        
        if device.isBidirectional {
            hints.append("Supports both input and output")
        } else if device.canRecord {
            hints.append("Input device for recording")
        } else if device.canPlayback {
            hints.append("Output device for playback")
        }
        
        return hints.joined(separator: ". ")
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    Group {
        DeviceRowView(device: AudioDevice.mockDevices[0], isSelected: true, conflicts: [])

        DeviceRowView(device: AudioDevice.mockDevices[1], isSelected: false, conflicts: [])

    }
}
#endif