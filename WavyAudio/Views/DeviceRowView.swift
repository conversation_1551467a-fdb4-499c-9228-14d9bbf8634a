import SwiftUI

struct DeviceRowView: View, Equatable {
    let device: AudioDevice
    let isSelected: Bool
    let conflicts: [SystemConflict]

    nonisolated static func == (lhs: DeviceRowView, rhs: DeviceRowView) -> Bool {
        // Equatable conformance for performance:
        // The view only needs to be redrawn if the device ID, selection state, or conflicts change.
        // This prevents re-rendering the entire view for minor property changes
        // that don't affect its visual representation here.
        lhs.device.id == rhs.device.id && 
        lhs.isSelected == rhs.isSelected &&
        lhs.conflicts.count == rhs.conflicts.count &&
        lhs.conflicts.map(\.severity) == rhs.conflicts.map(\.severity)
    }
    
    // Helper to get the highest severity for comparison
    private var highestSeverity: ConflictSeverity? {
        conflicts.max(by: { $0.severity.priority < $1.severity.priority })?.severity
    }
    
    // Helper to get the most relevant conflict for tooltip
    private var primaryConflict: SystemConflict? {
        conflicts.max(by: { $0.severity.priority < $1.severity.priority })
    }

    var body: some View {
        HStack {
            // Primary device icon with overlay for virtual/aggregate distinction
            ZStack(alignment: .bottomTrailing) {
                Image(systemName: device.deviceType.icon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(device.deviceType.color)
                
                // Secondary status icon for virtual/aggregate devices
                if let statusIcon = device.deviceType.deviceStatusIcon {
                    Image(systemName: statusIcon)
                        .font(.system(size: 8, weight: .semibold))
                        .foregroundColor(.white)
                        .background(Circle().fill(device.deviceType.color).frame(width: 12, height: 12))
                        .offset(x: 2, y: 2)
                }
            }

            VStack(alignment: .leading) {
                Text(device.displayName)
                    .font(.headline)
                    .lineLimit(1)
                Text(device.manufacturer)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            Spacer()
            VStack(alignment: .trailing) {
                Text(device.channelDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(device.sampleRateDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Conflict severity indicators with enhanced tooltip
            if !conflicts.isEmpty {
                conflictIndicators
                    .help(conflictsHelpText)
                    .onHover { isHovering in
                        // Add any hover effects here if needed
                    }
            }
        }
        .padding(.vertical, 4)
        .background(isSelected ? Color.accentColor.opacity(0.2) : Color.clear)
        .cornerRadius(5)
        // Enhanced accessibility
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityValue(accessibilityValue)
        .accessibilityHint(accessibilityHint)
        .accessibilityActions {
            if !conflicts.isEmpty {
                Button("View Conflicts") {
                    // Could trigger a conflict detail view
                }
            }
        }
    }
    
    // MARK: - Conflict Indicators
    
    @ViewBuilder
    private var conflictIndicators: some View {
        VStack(alignment: .trailing, spacing: 2) {
            // Enhanced count badge with severity-based styling
            Text("\(conflicts.count)")
                .font(.caption2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(minWidth: 16, minHeight: 16)
                .background(severityGradient)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(severityColor.opacity(0.8), lineWidth: 1)
                )
                .shadow(color: severityColor.opacity(0.3), radius: 2, x: 0, y: 1)
            
            // Enhanced severity icon with animation
            Image(systemName: severityIcon)
                .font(.caption)
                .foregroundColor(severityColor)
                .symbolRenderingMode(.hierarchical)
                .symbolEffect(.bounce, value: conflicts.count)
        }
        .accessibilityLabel("Device conflicts")
        .accessibilityValue("\(conflicts.count) conflicts, highest severity: \(highestSeverity?.rawValue ?? "none")")
    }
    
    // MARK: - Computed Properties
    
    private var severityColor: Color {
        guard let severity = highestSeverity else { return .secondary }
        return severity.color
    }
    
    private var severityGradient: LinearGradient {
        let colors: [Color]
        
        switch highestSeverity {
        case .info:
            colors = [.blue, .blue.opacity(0.7)]
        case .warning:
            colors = [.orange, .orange.opacity(0.7)]
        case .errorLevel:
            colors = [.red, .red.opacity(0.7)]
        case .critical:
            colors = [.purple, .pink.opacity(0.7)]
        case .none:
            colors = [.secondary, .secondary.opacity(0.7)]
        }
        
        return LinearGradient(
            gradient: Gradient(colors: colors),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var severityIcon: String {
        guard let severity = highestSeverity else { return "checkmark.circle" }
        return severity.iconName
    }
    
    private var conflictsHelpText: String {
        guard let primary = primaryConflict else { return "No conflicts" }
        
        let otherCount = conflicts.count - 1
        var helpText = "\(primary.severity.rawValue): \(primary.description)"
        
        if otherCount > 0 {
            helpText += "\n\n+\(otherCount) more conflict\(otherCount > 1 ? "s" : "")"
        }
        
        return helpText
    }
    
    // MARK: - Accessibility Properties
    
    private var accessibilityLabel: String {
        let deviceTypeLabel = device.deviceType.accessibilityDescription
        let statusLabel = isSelected ? "Selected" : ""
        return "\(device.displayName) \(deviceTypeLabel) \(statusLabel)".trimmingCharacters(in: .whitespaces)
    }
    
    private var accessibilityValue: String {
        var valueComponents: [String] = []
        
        // Device details
        valueComponents.append("Manufacturer: \(device.manufacturer)")
        valueComponents.append("Channels: \(device.channelDescription)")
        valueComponents.append("Sample rate: \(device.sampleRateDescription)")
        
        // Device capabilities
        var capabilities: [String] = []
        if device.canRecord { capabilities.append("recording") }
        if device.canPlayback { capabilities.append("playback") }
        if device.isVirtual { capabilities.append("virtual") }
        if device.isAggregate { capabilities.append("aggregate") }
        
        if !capabilities.isEmpty {
            valueComponents.append("Capabilities: \(capabilities.joined(separator: ", "))")
        }
        
        // Conflict information
        if !conflicts.isEmpty {
            let severityLevel = highestSeverity?.rawValue ?? "unknown"
            valueComponents.append("Conflicts: \(conflicts.count) (\(severityLevel) severity)")
        }
        
        return valueComponents.joined(separator: ". ")
    }
    
    private var accessibilityHint: String {
        var hints: [String] = []
        
        hints.append("Tap to select this audio device")
        
        if !conflicts.isEmpty {
            hints.append("Has conflicts that may affect performance")
        }
        
        if device.isBidirectional {
            hints.append("Supports both input and output")
        } else if device.canRecord {
            hints.append("Input device for recording")
        } else if device.canPlayback {
            hints.append("Output device for playback")
        }
        
        return hints.joined(separator: ". ")
    }
}

// MARK: - Preview

#if DEBUG
#Preview {
    Group {
        DeviceRowView(device: AudioDevice.mockDevices[0], isSelected: true, conflicts: [])

        DeviceRowView(device: AudioDevice.mockDevices[1], isSelected: false, conflicts: [])

    }
}
#endif