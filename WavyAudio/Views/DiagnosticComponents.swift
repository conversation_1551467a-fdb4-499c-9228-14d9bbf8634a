//
// DiagnosticComponents.swift - UI Components for Audio Diagnostics
// WWDC Compliance Reference:
// - WWDC25-286: SwiftUI fundamentals (Custom views, state management)
// - WWDC25-268: Swift 6 concurrency (@Observable integration)
// - WWDC21-10036: Sound Analysis (Real-time audio monitoring visualization)
//
// Status: NEW - Enhanced diagnostic UI components
//

import SwiftUI
import CoreAudio
import OSLog

// MARK: - Conflict Alerts View

struct ConflictAlertsView: View {
    let conflicts: [SystemConflict]
    @State private var expandedConflict: String?
    
    var body: some View {
        VStack(spacing: 2) {
            ForEach(conflicts.prefix(3), id: \.id) { conflict in
                HStack {
                    Image(systemName: conflict.severity.iconName)
                        .foregroundColor(conflict.severity.color)
                        .font(.caption)
                    
                    Text(conflict.description)
                        .font(.caption)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Button(action: {
                        expandedConflict = expandedConflict == conflict.id ? nil : conflict.id
                    }) {
                        Image(systemName: "info.circle")
                            .font(.caption)
                    }
                    .buttonStyle(.borderless)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 2)
                .background(conflict.severity.color.opacity(0.1))
                
                if expandedConflict == conflict.id {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Affected Devices:")
                            .font(.caption2)
                            .bold()
                        
                        ForEach(conflict.affectedDevices, id: \.self) { device in
                            Text("• \(device)")
                                .font(.caption2)
                        }
                        
                        if let resolution = conflict.suggestedResolution {
                            Text("Resolution:")
                                .font(.caption2)
                                .bold()
                            Text(resolution)
                                .font(.caption2)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.bottom, 4)
                    .background(conflict.severity.color.opacity(0.05))
                }
            }
            
            if conflicts.count > 3 {
                Text("\(conflicts.count - 3) more conflicts...")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.bottom, 2)
            }
        }
    }
}

// MARK: - Enhanced Device Row View

struct EnhancedDeviceRowView: View {
    let device: AudioDevice
    let isSelected: Bool
    let conflicts: [SystemConflict]
    let showDiagnostics: Bool
    
    var body: some View {
        HStack {
            // Device icon with conflict indicator overlay
            ZStack {
                Image(systemName: device.deviceType.icon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)
                    .foregroundColor(device.deviceType.color)
                
                if !conflicts.isEmpty {
                    Image(systemName: "exclamationmark.circle.fill")
                        .font(.caption2)
                        .foregroundColor(conflicts.first?.severity.color ?? .orange)
                        .offset(x: 8, y: -8)
                }
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(device.displayName)
                    .font(.headline)
                    .lineLimit(1)
                
                Text(device.manufacturer)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                // Show diagnostic info when enabled
                if showDiagnostics {
                    HStack(spacing: 8) {
                        Label("\(device.sampleRate, specifier: "%.0f") Hz", 
                              systemImage: "waveform")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        if let bufferSize = device.bufferSize {
                            Label("\(bufferSize) samples", 
                                  systemImage: "timer")
                                .font(.caption2)
                                .foregroundColor(bufferSize > 512 ? .orange : .secondary)
                        }
                        
                        if device.isInputConnected || device.isOutputConnected {
                            Label("Connected", systemImage: "checkmark.circle.fill")
                                .font(.caption2)
                                .foregroundColor(.green)
                        }
                    }
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(device.channelDescription)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if !showDiagnostics {
                    Text(device.sampleRateDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    // Show additional diagnostic info
                    if let latency = device.latency {
                        Text("\(latency, specifier: "%.1f")ms")
                            .font(.caption)
                            .foregroundColor(latency > 10 ? .orange : .secondary)
                    }
                }
                
                // Device status indicator
                deviceStatusIndicator
            }
        }
        .padding(.vertical, 4)
        .background(isSelected ? Color.accentColor.opacity(0.2) : Color.clear)
        .cornerRadius(5)
    }
    
    @ViewBuilder
    private var deviceStatusIndicator: some View {
        HStack(spacing: 2) {
            // Virtual/Aggregate device indicators
            if device.isVirtual {
                Image(systemName: "externaldrive.badge.wifi")
                    .font(.caption2)
                    .foregroundColor(.blue)
            }
            
            if device.isAggregate {
                Image(systemName: "square.stack.3d.up")
                    .font(.caption2)
                    .foregroundColor(.purple)
            }
            
            // Conflict severity indicator
            if let highestSeverity = conflicts.map(\.severity).max() {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.caption2)
                    .foregroundColor(highestSeverity.color)
            }
        }
    }
}

// MARK: - Diagnostic Panel View

struct DiagnosticPanelView: View {
    let diagnostics: AudioDiagnostics
    @State private var selectedTab = 0
    
    var body: some View {
        VStack(spacing: 0) {
            // Tab selector
            Picker("Diagnostic View", selection: $selectedTab) {
                Text("Conflicts").tag(0)
                Text("Performance").tag(1)
                Text("Console").tag(2)
            }
            .pickerStyle(.segmented)
            .padding(.horizontal, 8)
            .padding(.top, 8)
            
            // Content based on selected tab
            TabView(selection: $selectedTab) {
                ConflictsTabView(conflicts: diagnostics.realtimeConflicts)
                    .tag(0)
                
                PerformanceTabView(diagnostics: diagnostics)
                    .tag(1)
                
                ConsoleTabView(errors: diagnostics.consoleErrors)
                    .tag(2)
            }
            .tabViewStyle(.automatic)
        }
        .background(.regularMaterial)
        .overlay(alignment: .topTrailing) {
            Button(action: {
                // Generate quick diagnostic report
                Task {
                    _ = await diagnostics.generateFullDiagnosticReport()
                }
            }) {
                Image(systemName: "arrow.clockwise")
                    .font(.caption)
            }
            .buttonStyle(.borderless)
            .padding(8)
        }
    }
}

// MARK: - Diagnostic Tab Views

struct ConflictsTabView: View {
    let conflicts: [SystemConflict]
    
    var body: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 8) {
                if conflicts.isEmpty {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text("No conflicts detected")
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                } else {
                    ForEach(conflicts, id: \.id) { conflict in
                        ConflictDetailView(conflict: conflict)
                    }
                }
            }
            .padding(8)
        }
    }
}

struct PerformanceTabView: View {
    let diagnostics: AudioDiagnostics
    @State private var performanceData: AudioPerformanceData?
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 12) {
                if let data = performanceData {
                    PerformanceMetricRow(
                        title: "Core Audio CPU",
                        value: "\(data.cpuUsage.formatted(.number.precision(.fractionLength(1))))%",
                        status: data.cpuUsage > 50 ? .warning : .good
                    )
                    
                    PerformanceMetricRow(
                        title: "Audio Processes",
                        value: "\(data.processCount)",
                        status: data.processCount > 10 ? .warning : .good
                    )
                    
                    PerformanceMetricRow(
                        title: "Average Latency",
                        value: "\(data.averageLatency.formatted(.number.precision(.fractionLength(1))))ms",
                        status: data.averageLatency > 20 ? .warning : .good
                    )
                    
                    PerformanceMetricRow(
                        title: "Buffer Underruns",
                        value: "\(data.bufferUnderruns)",
                        status: data.bufferUnderruns > 0 ? .error : .good
                    )
                } else {
                    Text("Gathering performance data...")
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                }
            }
            .padding(8)
        }
        .onAppear {
            updatePerformanceData()
        }
    }
    
    private func updatePerformanceData() {
        // Simulate performance data gathering
        // In a real implementation, this would integrate with the diagnostics service
        performanceData = AudioPerformanceData(
            cpuUsage: Double.random(in: 5...15),
            processCount: Int.random(in: 3...8),
            averageLatency: Double.random(in: 5...15),
            bufferUnderruns: 0
        )
    }
}

struct ConsoleTabView: View {
    let errors: [String]
    
    var body: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 4) {
                if errors.isEmpty {
                    Text("No recent audio errors")
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .padding()
                } else {
                    ForEach(errors.suffix(20), id: \.self) { error in
                        Text(error)
                            .font(.system(.caption, design: .monospaced))
                            .foregroundColor(.secondary)
                            .lineLimit(nil)
                            .textSelection(.enabled)
                    }
                }
            }
            .padding(8)
        }
    }
}

// MARK: - Supporting Views

struct ConflictDetailView: View {
    let conflict: SystemConflict
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: conflict.severity.iconName)
                    .foregroundColor(conflict.severity.color)
                
                Text(conflict.type.description)
                    .font(.headline)
                    .foregroundColor(conflict.severity.color)
                
                Spacer()
                
                Text(conflict.severity.description)
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(conflict.severity.color.opacity(0.2))
                    .cornerRadius(4)
            }
            
            Text(conflict.description)
                .font(.caption)
                .foregroundColor(.primary)
            
            if !conflict.affectedDevices.isEmpty {
                Text("Affected: \(conflict.affectedDevices.joined(separator: ", "))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(8)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 6))
    }
}

struct PerformanceMetricRow: View {
    let title: String
    let value: String
    let status: MetricStatus
    
    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
            
            Spacer()
            
            HStack(spacing: 4) {
                Image(systemName: status.iconName)
                    .foregroundColor(status.color)
                    .font(.caption2)
                
                Text(value)
                    .font(.caption)
                    .bold()
                    .foregroundColor(status.color)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(status.color.opacity(0.1), in: RoundedRectangle(cornerRadius: 4))
    }
}

// MARK: - Supporting Types

struct AudioPerformanceData {
    let cpuUsage: Double
    let processCount: Int
    let averageLatency: Double
    let bufferUnderruns: Int
}

enum MetricStatus {
    case good, warning, error
    
    var color: Color {
        switch self {
        case .good: return .green
        case .warning: return .orange
        case .error: return .red
        }
    }
    
    var iconName: String {
        switch self {
        case .good: return "checkmark.circle.fill"
        case .warning: return "exclamationmark.triangle.fill"
        case .error: return "xmark.circle.fill"
        }
    }
}

extension AudioDevice {
    var bufferSize: UInt32? {
        // This would need to be implemented in the actual AudioDevice model
        // For now, return a placeholder
        return nil
    }
    
    var latency: Double? {
        // This would need to be implemented in the actual AudioDevice model
        // For now, return a placeholder based on sample rate
        return 1000.0 / sampleRate * 64 // Rough estimate for 64-sample buffer
    }
}

extension ConflictSeverity: Comparable {
    public static func < (lhs: ConflictSeverity, rhs: ConflictSeverity) -> Bool {
        let order: [ConflictSeverity] = [.info, .warning, .error, .critical]
        guard let lhsIndex = order.firstIndex(of: lhs),
              let rhsIndex = order.firstIndex(of: rhs) else {
            return false
        }
        return lhsIndex < rhsIndex
    }
}