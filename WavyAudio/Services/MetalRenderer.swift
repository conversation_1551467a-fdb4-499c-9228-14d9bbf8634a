//
// MetalRenderer.swift - Metal Shader Rendering Service
// WWDC Compliance Reference:
// - WWDC19-608: Metal for Pro Apps (GPU workload distribution, optimization)
// - WWDC25-245: Xcode 26 features (Metal debugging, performance profiling)
// - WWDC21-10036: Sound Analysis (GPU-accelerated audio visualization)
//

import Foundation
import Metal
import MetalKit
import simd
import OSLog

@MainActor
class MetalRenderer {
    private let logger = Logger(subsystem: "com.wavyaudio.metal", category: "Renderer")
    
    // Metal objects
    private var device: MTLDevice
    private var commandQueue: MTLCommandQueue
    private var library: MTLLibrary
    
    // Render pipelines
    private var waveformPipeline: MTLRenderPipelineState?
    private var spectrumPipeline: MTLRenderPipelineState?
    private var vuMeterPipeline: MTLRenderPipelineState?
    private var gridPipeline: MTLRenderPipelineState?
    private var nodePipeline: MTLRenderPipelineState?
    private var connectionPipeline: MTLRenderPipelineState?
    
    // Uniform buffers
    private var uniformBuffer: MTLBuffer
    private var canvasUniformBuffer: MTLBuffer
    
    // Textures for audio data
    private var audioTexture: MTLTexture?
    private var fftTexture: MTLTexture?
    
    init() throws {
        // Initialize Metal device
        guard let device = MTLCreateSystemDefaultDevice() else {
            throw MetalError.deviceCreationFailed
        }
        self.device = device
        
        // Create command queue
        guard let commandQueue = device.makeCommandQueue() else {
            throw MetalError.commandQueueCreationFailed
        }
        self.commandQueue = commandQueue
        
        // Load shader library
        guard let library = device.makeDefaultLibrary() else {
            throw MetalError.libraryCreationFailed
        }
        self.library = library
        
        // Create uniform buffers
        guard let uniformBuffer = device.makeBuffer(length: MemoryLayout<Uniforms>.stride, options: [.storageModeShared]),
              let canvasUniformBuffer = device.makeBuffer(length: MemoryLayout<CanvasUniforms>.stride, options: [.storageModeShared]) else {
            throw MetalError.bufferCreationFailed
        }
        self.uniformBuffer = uniformBuffer
        self.canvasUniformBuffer = canvasUniformBuffer
        
        // Setup render pipelines
        try setupRenderPipelines()
        
        logger.info("Metal renderer initialized successfully")
    }
    
    // MARK: - Pipeline Setup
    
    private func setupRenderPipelines() throws {
        // Audio visualization pipelines
        waveformPipeline = try createRenderPipeline(
            vertexFunction: "waveform_vertex",
            fragmentFunction: "waveform_fragment"
        )
        
        spectrumPipeline = try createRenderPipeline(
            vertexFunction: "waveform_vertex",
            fragmentFunction: "spectrum_fragment"
        )
        
        vuMeterPipeline = try createRenderPipeline(
            vertexFunction: "waveform_vertex",
            fragmentFunction: "vu_meter_fragment"
        )
        
        // Canvas effect pipelines
        gridPipeline = try createRenderPipeline(
            vertexFunction: "grid_vertex",
            fragmentFunction: "grid_fragment"
        )
        
        nodePipeline = try createRenderPipeline(
            vertexFunction: "waveform_vertex",
            fragmentFunction: "node_glow_fragment"
        )
        
        connectionPipeline = try createRenderPipeline(
            vertexFunction: "connection_vertex",
            fragmentFunction: "connection_fragment"
        )
    }
    
    private func createRenderPipeline(vertexFunction: String, fragmentFunction: String) throws -> MTLRenderPipelineState {
        let descriptor = MTLRenderPipelineDescriptor()
        
        guard let vertexFunc = library.makeFunction(name: vertexFunction),
              let fragmentFunc = library.makeFunction(name: fragmentFunction) else {
            throw MetalError.functionNotFound
        }
        
        descriptor.vertexFunction = vertexFunc
        descriptor.fragmentFunction = fragmentFunc
        
        // Configure color attachment
        descriptor.colorAttachments[0].pixelFormat = .bgra8Unorm
        descriptor.colorAttachments[0].isBlendingEnabled = true
        descriptor.colorAttachments[0].rgbBlendOperation = .add
        descriptor.colorAttachments[0].alphaBlendOperation = .add
        descriptor.colorAttachments[0].sourceRGBBlendFactor = .sourceAlpha
        descriptor.colorAttachments[0].sourceAlphaBlendFactor = .sourceAlpha
        descriptor.colorAttachments[0].destinationRGBBlendFactor = .oneMinusSourceAlpha
        descriptor.colorAttachments[0].destinationAlphaBlendFactor = .oneMinusSourceAlpha
        
        // Configure vertex descriptor
        let vertexDescriptor = MTLVertexDescriptor()
        vertexDescriptor.attributes[0].format = .float2
        vertexDescriptor.attributes[0].offset = 0
        vertexDescriptor.attributes[0].bufferIndex = 0
        
        vertexDescriptor.attributes[1].format = .float2
        vertexDescriptor.attributes[1].offset = 8
        vertexDescriptor.attributes[1].bufferIndex = 0
        
        vertexDescriptor.layouts[0].stride = 16
        vertexDescriptor.layouts[0].stepRate = 1
        vertexDescriptor.layouts[0].stepFunction = .perVertex
        
        descriptor.vertexDescriptor = vertexDescriptor
        
        return try device.makeRenderPipelineState(descriptor: descriptor)
    }
    
    // MARK: - Audio Data Updates
    
    func updateAudioData(_ samples: [Float]) {
        let textureDescriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .r32Float,
            width: samples.count,
            height: 1,
            mipmapped: false
        )
        textureDescriptor.usage = [.shaderRead]
        
        audioTexture = device.makeTexture(descriptor: textureDescriptor)
        
        samples.withUnsafeBytes { bytes in
            audioTexture?.replace(
                region: MTLRegionMake2D(0, 0, samples.count, 1),
                mipmapLevel: 0,
                withBytes: bytes.baseAddress!,
                bytesPerRow: samples.count * MemoryLayout<Float>.stride
            )
        }
    }
    
    func updateFFTData(_ magnitudes: [Float]) {
        let textureDescriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .r32Float,
            width: magnitudes.count,
            height: 1,
            mipmapped: false
        )
        textureDescriptor.usage = [.shaderRead]
        
        fftTexture = device.makeTexture(descriptor: textureDescriptor)
        
        magnitudes.withUnsafeBytes { bytes in
            fftTexture?.replace(
                region: MTLRegionMake2D(0, 0, magnitudes.count, 1),
                mipmapLevel: 0,
                withBytes: bytes.baseAddress!,
                bytesPerRow: magnitudes.count * MemoryLayout<Float>.stride
            )
        }
    }
    
    // MARK: - Rendering Methods
    
    func renderWaveform(to inputTexture: MTLTexture, time: Float, amplitude: Float, frequency: Float) {
        guard let pipeline = waveformPipeline else { return }
        
        let uniforms = Uniforms(
            projectionMatrix: matrix_identity_float4x4,
            time: time,
            amplitude: amplitude,
            frequency: frequency,
            sampleRate: 48000.0
        )
        
        uniformBuffer.contents().bindMemory(to: Uniforms.self, capacity: 1).pointee = uniforms
        
        render(pipeline: pipeline, renderTexture: inputTexture, uniformBuffer: uniformBuffer, inputTexture: audioTexture)
    }
    
    func renderSpectrum(to inputTexture: MTLTexture, time: Float) {
        guard let pipeline = spectrumPipeline else { return }
        
        let uniforms = Uniforms(
            projectionMatrix: matrix_identity_float4x4,
            time: time,
            amplitude: 1.0,
            frequency: 1.0,
            sampleRate: 48000.0
        )
        
        uniformBuffer.contents().bindMemory(to: Uniforms.self, capacity: 1).pointee = uniforms
        
        render(pipeline: pipeline, renderTexture: inputTexture, uniformBuffer: uniformBuffer, inputTexture: fftTexture)
    }
    
    func renderGrid(to inputTexture: MTLTexture, zoom: Float, offset: simd_float2, time: Float) {
        guard let pipeline = gridPipeline else { return }
        
        let canvasUniforms = CanvasUniforms(
            projectionMatrix: matrix_identity_float4x4,
            viewMatrix: matrix_identity_float4x4,
            time: time,
            zoom: zoom,
            offset: offset,
            gridSpacing: 50.0,
            nodeCount: 10.0
        )
        
        canvasUniformBuffer.contents().bindMemory(to: CanvasUniforms.self, capacity: 1).pointee = canvasUniforms
        
        render(pipeline: pipeline, renderTexture: inputTexture, uniformBuffer: canvasUniformBuffer, inputTexture: nil)
    }
    
    // MARK: - Private Rendering
    
    private func render(pipeline: MTLRenderPipelineState, renderTexture: MTLTexture, uniformBuffer: MTLBuffer, inputTexture: MTLTexture?) {
        guard let commandBuffer = commandQueue.makeCommandBuffer() else { return }
        
        let renderPassDescriptor = MTLRenderPassDescriptor()
        renderPassDescriptor.colorAttachments[0].texture = renderTexture
        renderPassDescriptor.colorAttachments[0].loadAction = .clear
        renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColor(red: 0, green: 0, blue: 0, alpha: 0)
        renderPassDescriptor.colorAttachments[0].storeAction = .store
        
        guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else { return }
        
        renderEncoder.setRenderPipelineState(pipeline)
        renderEncoder.setVertexBuffer(uniformBuffer, offset: 0, index: 0)
        renderEncoder.setFragmentBuffer(uniformBuffer, offset: 0, index: 0)
        
        if let inputTexture = inputTexture {
            renderEncoder.setFragmentTexture(inputTexture, index: 0)
        }
        
        // Draw full-screen quad
        renderEncoder.drawPrimitives(type: .triangleStrip, vertexStart: 0, vertexCount: 4)
        
        renderEncoder.endEncoding()
        commandBuffer.commit()
    }
}

// MARK: - Supporting Types

struct Uniforms {
    let projectionMatrix: simd_float4x4
    let time: Float
    let amplitude: Float
    let frequency: Float
    let sampleRate: Float
}

struct CanvasUniforms {
    let projectionMatrix: simd_float4x4
    let viewMatrix: simd_float4x4
    let time: Float
    let zoom: Float
    let offset: simd_float2
    let gridSpacing: Float
    let nodeCount: Float
}

enum MetalError: Error {
    case deviceCreationFailed
    case commandQueueCreationFailed
    case libraryCreationFailed
    case functionNotFound
    case bufferCreationFailed
    case pipelineCreationFailed
}