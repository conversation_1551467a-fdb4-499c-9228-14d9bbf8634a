//
// MetalAudioProcessor.swift - GPU-accelerated audio processing
// WWDC Compliance Reference:
// - WWDC25-10021: Metal 3.5 features (GPU-driven pipelines, mesh shaders)
// - WWDC25-10187: MetalFX (Upscaling and frame generation)
// - WWDC25-10110: Optimize Metal apps and games (Performance best practices)
// - WWDC25-10023: Build immersive apps with RealityKit and Metal
//
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-10021
//

import Metal
import MetalKit
import MetalPerformanceShaders
import AVFoundation
import Accelerate
import OSLog

@available(macOS 15.0, *)
@MainActor
class MetalAudioProcessor: ObservableObject {
    // MARK: - Properties
    
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let library: MTLLibrary
    
    // Pipeline states
    private var audioGainPipeline: MTLComputePipelineState?
    private var multiBandPipeline: MTLComputePipelineState?
    private var visualizationPipeline: MTLComputePipelineState?
    private var spectrumPipeline: MTLComputePipelineState?
    private var meshPipeline: MTLRenderPipelineState?
    
    // GPU-driven pipeline (WWDC 2025)
    private var gpuDispatchPipeline: MTLComputePipelineState?
    private var indirectCommandBuffer: MTLIndirectCommandBuffer?
    
    // Audio parameters
    @Published var volume: Float = 1.0
    @Published var isProcessing: Bool = false
    private var previousVolume: Float = 1.0
    private let sampleRate: Double
    private let channelCount: Int
    
    // Processing queue
    private let processingQueue = DispatchQueue(label: "com.wavyaudio.metalprocessor", qos: .userInitiated)
    
    // Buffers
    private var audioParamsBuffer: MTLBuffer?
    private var visualizationBuffer: MTLBuffer?
    private var spectrumBuffer: MTLBuffer?
    
    // MetalFX support (WWDC 2025)
    private var metalFXUpscaler: Any? // MTLFXSpatialScaler when available
    
    // Logger
    private let logger = Logger(subsystem: "com.wavyaudio.metal", category: "AudioProcessor")
    
    // MARK: - Initialization
    
    init?(sampleRate: Double, channelCount: Int) {
        guard let device = MTLCreateSystemDefaultDevice(),
              let commandQueue = device.makeCommandQueue() else {
            return nil
        }
        
        self.device = device
        self.commandQueue = commandQueue
        self.sampleRate = sampleRate
        self.channelCount = channelCount
        
        // Load shader library
        do {
            // First try to load compiled metallib
            if let libraryURL = Bundle.main.url(forResource: "default", withExtension: "metallib") {
                library = try device.makeLibrary(URL: libraryURL)
            } else {
                // Fall back to default library
                guard let defaultLibrary = device.makeDefaultLibrary() else {
                    logger.error("Failed to create default Metal library")
                    return nil
                }
                library = defaultLibrary
            }
        } catch {
            logger.error("Failed to load Metal library: \(error.localizedDescription)")
            return nil
        }
        
        setupPipelines()
        setupBuffers()
        setupMetalFX()
    }
    
    private func setupPipelines() {
        // Audio gain pipeline
        if let function = library.makeFunction(name: "audioGainKernel") {
            do {
                audioGainPipeline = try device.makeComputePipelineState(function: function)
            } catch {
                logger.error("Failed to create audio gain pipeline: \(error.localizedDescription)")
            }
        }
        
        // Multi-band dynamics pipeline
        if let function = library.makeFunction(name: "multiBandDynamicsKernel") {
            do {
                multiBandPipeline = try device.makeComputePipelineState(function: function)
            } catch {
                logger.error("Failed to create multi-band pipeline: \(error.localizedDescription)")
            }
        }
        
        // Visualization pipeline
        if let function = library.makeFunction(name: "waveformVisualizationKernel") {
            do {
                visualizationPipeline = try device.makeComputePipelineState(function: function)
            } catch {
                logger.error("Failed to create visualization pipeline: \(error.localizedDescription)")
            }
        }
        
        // Spectrum analysis pipeline
        if let function = library.makeFunction(name: "spectrumAnalysisKernel") {
            do {
                spectrumPipeline = try device.makeComputePipelineState(function: function)
            } catch {
                logger.error("Failed to create spectrum pipeline: \(error.localizedDescription)")
            }
        }
        
        // GPU-driven dispatcher (WWDC 2025)
        if let function = library.makeFunction(name: "audioPipelineDispatcher") {
            do {
                gpuDispatchPipeline = try device.makeComputePipelineState(function: function)
                setupIndirectCommandBuffer()
            } catch {
                logger.error("Failed to create GPU dispatch pipeline: \(error.localizedDescription)")
            }
        }
        
        // Mesh shader pipeline for visualization (Metal 3.5)
        setupMeshPipeline()
    }
    
    private func setupMeshPipeline() {
        guard device.supportsFamily(.apple9) else {
            logger.info("Mesh shaders not supported on this device")
            return
        }
        
        let descriptor = MTLMeshRenderPipelineDescriptor()
        descriptor.label = "Audio Visualization Mesh Pipeline"
        
        if let meshFunction = library.makeFunction(name: "audioVisualizationMesh"),
           let fragmentFunction = library.makeFunction(name: "audioVisualizationFragment") {
            descriptor.meshFunction = meshFunction
            descriptor.fragmentFunction = fragmentFunction
            descriptor.colorAttachments[0].pixelFormat = .bgra8Unorm
            
            do {
                let (pipeline, _) = try device.makeRenderPipelineState(descriptor: descriptor, options: [])
                meshPipeline = pipeline
            } catch {
                logger.error("Failed to create mesh pipeline: \(error.localizedDescription)")
            }
        }
    }
    
    private func setupBuffers() {
        // Audio processing parameters buffer
        let paramsSize = MemoryLayout<AudioProcessingParams>.stride
        audioParamsBuffer = device.makeBuffer(length: paramsSize, options: .storageModeShared)
        
        // Visualization buffer (for waveform points)
        let vizPointCount = 1024
        let vizSize = vizPointCount * MemoryLayout<SIMD2<Float>>.stride
        visualizationBuffer = device.makeBuffer(length: vizSize, options: .storageModeShared)
        
        // Spectrum buffer
        let spectrumSize = 512 * MemoryLayout<Float>.stride
        spectrumBuffer = device.makeBuffer(length: spectrumSize, options: .storageModeShared)
    }
    
    private func setupIndirectCommandBuffer() {
        guard let dispatcher = gpuDispatchPipeline else { return }
        
        let descriptor = MTLIndirectCommandBufferDescriptor()
        descriptor.commandTypes = [.computeDispatch]
        descriptor.inheritBuffers = false
        descriptor.inheritPipelineState = false
        descriptor.maxKernelBufferBindCount = 4
        
        guard let icb = device.makeIndirectCommandBuffer(descriptor: descriptor, maxCommandCount: 10, options: []) else {
            logger.error("Failed to create indirect command buffer")
            return
        }
        
        indirectCommandBuffer = icb
    }
    
    private func setupMetalFX() {
        // Setup MetalFX spatial upscaler for visualization (WWDC 2025)
        // This would be used for upscaling low-resolution audio visualizations
        // Note: MetalFX API details would be available in WWDC 2025
        logger.info("MetalFX setup placeholder - awaiting WWDC 2025 APIs")
    }
    
    // MARK: - Audio Processing
    
    func process(audioBuffer: AVAudioPCMBuffer) -> AVAudioPCMBuffer? {
        guard isProcessing,
              let outputBuffer = AVAudioPCMBuffer(
                pcmFormat: audioBuffer.format,
                frameCapacity: audioBuffer.frameLength
              ) else {
            return nil
        }
        
        processingQueue.sync {
            // Convert input buffer to Metal buffers (one per channel)
            let frameCount = Int(audioBuffer.frameLength)
            let channelCount = Int(audioBuffer.format.channelCount)
            
            // Process each channel
            for channel in 0..<channelCount {
                guard let channelData = audioBuffer.floatChannelData?[channel] else { continue }
                
                // Create or reuse Metal buffer
                let bufferSize = frameCount * MemoryLayout<Float>.stride
                let inputBuffer = device.makeBuffer(
                    bytes: channelData,
                    length: bufferSize,
                    options: .storageModeShared
                )
                
                let outputBuffer = device.makeBuffer(
                    length: bufferSize,
                    options: .storageModeShared
                )
                
                // Process the audio with Metal
                processChannel(
                    input: inputBuffer,
                    output: outputBuffer,
                    frameCount: frameCount,
                    channel: channel
                )
                
                // Copy processed data back to output buffer
                if let outputData = outputBuffer?.contents() {
                    let outputFloatData = outputData.bindMemory(
                        to: Float.self,
                        capacity: frameCount
                    )
                    
                    memcpy(
                        outputBuffer.floatChannelData?[channel],
                        outputFloatData,
                        bufferSize
                    )
                }
            }
            
            outputBuffer.frameLength = audioBuffer.frameLength
        }
        
        return outputBuffer
    }
    
    private func processChannel(
        input: MTLBuffer?,
        output: MTLBuffer?,
        frameCount: Int,
        channel: Int
    ) {
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder(),
              let pipelineState = audioGainPipeline,
              let input = input,
              let output = output,
              let paramsBuffer = audioParamsBuffer else {
            return
        }
        
        // Update audio parameters
        updateAudioParams(frameCount: frameCount)
        
        // Configure the compute encoder
        encoder.setComputePipelineState(pipelineState)
        encoder.setBuffer(input, offset: 0, index: 0)
        encoder.setBuffer(output, offset: 0, index: 1)
        encoder.setBuffer(paramsBuffer, offset: 0, index: 2)
        
        // Set up thread groups
        let threadGroupSize = MTLSize(
            width: min(pipelineState.maxTotalThreadsPerThreadgroup, frameCount),
            height: 1,
            depth: 1
        )
        
        let gridSize = MTLSize(
            width: frameCount,
            height: 1,
            depth: 1
        )
        
        // Dispatch the compute kernel
        encoder.dispatchThreads(
            gridSize,
            threadsPerThreadgroup: threadGroupSize
        )
        
        encoder.endEncoding()
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        // Update previous volume for smoothing
        previousVolume = volume
    }
    
    private func updateAudioParams(frameCount: Int) {
        guard let paramsBuffer = audioParamsBuffer else { return }
        
        let params = paramsBuffer.contents().bindMemory(to: AudioProcessingParams.self, capacity: 1)
        params.pointee.volume = volume
        params.pointee.previousVolume = previousVolume
        params.pointee.sampleRate = Float(sampleRate)
        params.pointee.channelCount = UInt32(channelCount)
        params.pointee.frameCount = UInt32(frameCount)
    }
    
    // MARK: - Control
    
    func start() {
        processingQueue.sync {
            isProcessing = true
        }
    }
    
    func stop() {
        processingQueue.sync {
            isProcessing = false
        }
    }
    
    func setVolume(_ volume: Float) {
        processingQueue.sync {
            self.volume = min(max(volume, 0.0), 1.0)
            // Update shader parameters if needed
        }
    }
    
    // MARK: - Visualization
    
    func generateWaveformVisualization(from audioBuffer: AVAudioPCMBuffer) -> [SIMD2<Float>]? {
        guard let visualizationPipeline = visualizationPipeline,
              let visualizationBuffer = visualizationBuffer,
              let channelData = audioBuffer.floatChannelData?[0] else {
            return nil
        }
        
        let frameCount = Int(audioBuffer.frameLength)
        let pointCount = 1024
        
        // Create Metal buffer from audio data
        guard let inputBuffer = device.makeBuffer(
            bytes: channelData,
            length: frameCount * MemoryLayout<Float>.stride,
            options: .storageModeShared
        ) else { return nil }
        
        // Create command buffer and encoder
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            return nil
        }
        
        // Set up buffers
        encoder.setComputePipelineState(visualizationPipeline)
        encoder.setBuffer(inputBuffer, offset: 0, index: 0)
        encoder.setBuffer(visualizationBuffer, offset: 0, index: 1)
        
        var bufferSize = UInt32(frameCount)
        encoder.setBytes(&bufferSize, length: MemoryLayout<UInt32>.size, index: 2)
        
        var vizPointCount = UInt32(pointCount)
        encoder.setBytes(&vizPointCount, length: MemoryLayout<UInt32>.size, index: 3)
        
        // Dispatch
        let threadGroupSize = MTLSize(
            width: min(visualizationPipeline.maxTotalThreadsPerThreadgroup, pointCount),
            height: 1,
            depth: 1
        )
        
        let gridSize = MTLSize(width: pointCount, height: 1, depth: 1)
        encoder.dispatchThreads(gridSize, threadsPerThreadgroup: threadGroupSize)
        
        encoder.endEncoding()
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        // Read back results
        let points = visualizationBuffer.contents().bindMemory(to: SIMD2<Float>.self, capacity: pointCount)
        return Array(UnsafeBufferPointer(start: points, count: pointCount))
    }
    
    // MARK: - GPU-Driven Pipeline (WWDC 2025)
    
    func dispatchGPUDrivenPipeline(commands: [UInt32]) {
        guard let gpuDispatchPipeline = gpuDispatchPipeline,
              let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            return
        }
        
        // Create command buffer
        guard let commandsBuffer = device.makeBuffer(
            bytes: commands,
            length: commands.count * MemoryLayout<UInt32>.stride,
            options: .storageModeShared
        ) else { return }
        
        // Create state buffer
        guard let stateBuffer = device.makeBuffer(
            length: MemoryLayout<UInt32>.stride,
            options: .storageModeShared
        ) else { return }
        
        // Initialize state
        stateBuffer.contents().bindMemory(to: UInt32.self, capacity: 1).pointee = 0
        
        // Dispatch
        encoder.setComputePipelineState(gpuDispatchPipeline)
        encoder.setBuffer(commandsBuffer, offset: 0, index: 0)
        encoder.setBuffer(stateBuffer, offset: 0, index: 1)
        
        var commandCount = UInt32(commands.count)
        encoder.setBytes(&commandCount, length: MemoryLayout<UInt32>.size, index: 2)
        
        let threadGroupSize = MTLSize(
            width: min(gpuDispatchPipeline.maxTotalThreadsPerThreadgroup, commands.count),
            height: 1,
            depth: 1
        )
        
        let gridSize = MTLSize(width: commands.count, height: 1, depth: 1)
        encoder.dispatchThreads(gridSize, threadsPerThreadgroup: threadGroupSize)
        
        encoder.endEncoding()
        commandBuffer.commit()
    }
}

// MARK: - Supporting Types

struct AudioProcessingParams {
    var volume: Float
    var previousVolume: Float
    var sampleRate: Float
    var channelCount: UInt32
    var frameCount: UInt32
}
