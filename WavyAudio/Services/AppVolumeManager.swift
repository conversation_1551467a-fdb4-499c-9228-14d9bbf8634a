import AVFoundation
import os.log

// Note: This is a macOS-only implementation
// AVAudioSession is iOS-only, so we use Core Audio HAL for macOS
@available(macOS 14.0, *)
class AppVolumeManager: @unchecked Sendable {
    // MARK: - Properties

    static let shared = AppVolumeManager()

    // Use Core Audio HAL instead of AVAudioSession for macOS
    private let logger = Logger(subsystem: "com.wavyaudio.volume", category: "AppVolume")
    private var volumeObservers = [NSKeyValueObservation]()
    private var audioRouteObservers = [NSObjectProtocol]()
    private var isActive = false
    
    // MARK: - Public API
    
    func start() {
        guard !isActive else { return }
        
        do {
            // Configure audio session with the latest features
            try audioSession.setCategory(
                .playAndRecord,
                mode: .voiceChat,
                policy: .default,
                options: [
                    .mixWithOthers,
                    .allowBluetooth,
                    .allowBluetoothA2DP,
                    .allowAirPlay,
                    .interruptSpokenAudioAndMixWithOthers
                ]
            )
            
            // Enable per-app volume control
            try audioSession.setPreferredIOBufferDuration(0.005) // 5ms buffer for low latency
            try audioSession.setAllowHapticsAndSystemSoundsDuringRecording(true)
            
            // Activate the session
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            
            // Set up volume observation
            setupVolumeObservers()
            setupRouteChangeHandling()
            
            isActive = true
            logger.info("App volume manager started")
            
        } catch {
            logger.error("Failed to start AppVolumeManager: \(error.localizedDescription)")
        }
    }
    
    func stop() {
        volumeObservers.removeAll()
        audioRouteObservers.forEach { NotificationCenter.default.removeObserver($0) }
        audioRouteObservers.removeAll()
        
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            isActive = false
            logger.info("App volume manager stopped")
        } catch {
            logger.error("Failed to stop AppVolumeManager: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Volume Control
    
    func setVolume(_ volume: Float, forApp bundleIdentifier: String) {
        guard isActive else { return }
        
        // In a real implementation, this would use the new AVAudioSession APIs for per-app volume
        // For now, we'll demonstrate the concept with system volume
        let clampedVolume = max(0.0, min(1.0, volume))
        
        // This is a simplified example - in a real implementation, you would use:
        // AVAudioSession.sharedInstance().setVolume(clampedVolume, forAudioSessionID: sessionID)
        
        // For demonstration, we'll use MPVolumeView for system volume
        let volumeView = MPVolumeView()
        if let slider = volumeView.subviews.first(where: { $0 is UISlider }) as? UISlider {
            slider.value = clampedVolume
        }
        
        logger.info("Set volume to \(clampedVolume) for app: \(bundleIdentifier)")
    }
    
    func getVolume(forApp bundleIdentifier: String) -> Float {
        // In a real implementation, this would query the system for the app's volume
        // For now, we'll return the system volume
        return audioSession.outputVolume
    }
    
    // MARK: - Private Methods
    
    private func setupVolumeObservers() {
        // Observe system volume changes
        let volumeObserver = audioSession.observe(\.outputVolume) { [weak self] (session, _) in
            self?.handleVolumeChange(volume: session.outputVolume)
        }
        volumeObservers.append(volumeObserver)
    }
    
    private func setupRouteChangeHandling() {
        let observer = NotificationCenter.default.addObserver(
            forName: AVAudioSession.routeChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleRouteChange(notification: notification)
        }
        audioRouteObservers.append(observer)
    }
    
    private func handleVolumeChange(volume: Float) {
        // Handle volume changes from hardware controls
        logger.debug("System volume changed to: \(volume)")
        
        // Notify any observers about the volume change
        NotificationCenter.default.post(
            name: .appVolumeDidChange,
            object: nil,
            userInfo: ["volume": volume]
        )
    }
    
    private func handleRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }
        
        switch reason {
        case .newDeviceAvailable, .oldDeviceUnavailable:
            let currentRoute = audioSession.currentRoute
            logger.info("Audio route changed. Current route: \(currentRoute)")
            
        case .categoryChange:
            logger.info("Audio session category changed")
            
        case .override:
            logger.info("Audio route was overridden")
            
        default:
            logger.debug("Audio route changed for reason: \(reason.rawValue)")
        }
    }
}

// MARK: - Notifications

extension Notification.Name {
    static let appVolumeDidChange = Notification.Name("com.wavyaudio.volume.didChange")
}

// MARK: - macOS Core Audio Extensions
// Note: AVAudioSession is not available on macOS, so we implement macOS-specific volume control

extension AppVolumeManager {
    /// Get volume for a specific application using Core Audio HAL
    private func getApplicationVolume(for bundleIdentifier: String) -> Float? {
        // macOS implementation using Core Audio HAL
        // This would require detecting application audio streams
        // and controlling them through virtual audio devices
        return nil
    }

    /// Set volume for a specific application using Core Audio HAL
    private func setApplicationVolume(_ volume: Float, for bundleIdentifier: String) throws {
        // macOS implementation using Core Audio HAL
        // This would require routing application audio through virtual devices
        throw AppVolumeError.notSupported
    }
}
