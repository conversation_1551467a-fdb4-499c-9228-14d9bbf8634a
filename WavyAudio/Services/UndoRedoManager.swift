//
// UndoRedoManager.swift - Professional Undo/Redo System
// WWDC Compliance Reference:
// - WWDC25-268: Swift 6 concurrency (@Observable pattern, async operations)
// - WWDC25-319: SwiftUI essentials (State management, user interactions)
// - WWDC23-10165: Fix failures faster with Xcode test reports (Testing undo/redo)
//
// Status: NEW - Professional workflow action history management
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-268
//

import Foundation
import SwiftUI
import Combine
import OSLog
import CoreAudio

// MARK: - Action Types

@MainActor
protocol UndoableAction: Codable {
    var actionType: ActionType { get }
    var timestamp: Date { get }
    var description: String { get }
    
    func execute() async throws
    func undo() async throws
}

enum ActionType: String, CaseIterable, Codable {
    case createConnection = "Create Connection"
    case deleteConnection = "Delete Connection"
    case moveNode = "Move Node"
    case createNode = "Create Node"
    case deleteNode = "Delete Node"
    case loadPreset = "Load Preset"
    case savePreset = "Save Preset"
    case changeSettings = "Change Settings"
    case autoLayout = "Auto Layout"
    case clearAll = "Clear All"
}

// MARK: - Specific Action Implementations

struct CreateConnectionAction: UndoableAction {
    var actionType: ActionType { .createConnection }
    let timestamp: Date
    let sourceDeviceID: AudioObjectID
    let destinationDeviceID: AudioObjectID
    let connectionType: AudioConnectionType
    
    var description: String {
        "Create connection from device \(sourceDeviceID) to \(destinationDeviceID)"
    }
    
    init(sourceDeviceID: AudioObjectID, destinationDeviceID: AudioObjectID, connectionType: AudioConnectionType) {
        self.timestamp = Date()
        self.sourceDeviceID = sourceDeviceID
        self.destinationDeviceID = destinationDeviceID
        self.connectionType = connectionType
    }
    
    func execute() async throws {
        // Implementation would connect to AudioEngineManager
        // audioEngineManager.createConnection(from: sourceDeviceID, to: destinationDeviceID, type: connectionType)
    }
    
    func undo() async throws {
        // Implementation would remove connection
        // audioEngineManager.removeConnection(from: sourceDeviceID, to: destinationDeviceID)
    }
}

struct DeleteConnectionAction: UndoableAction {
    var actionType: ActionType { .deleteConnection }
    let timestamp: Date
    let sourceDeviceID: AudioObjectID
    let destinationDeviceID: AudioObjectID
    let connectionType: AudioConnectionType
    
    var description: String {
        "Delete connection from device \(sourceDeviceID) to \(destinationDeviceID)"
    }
    
    init(sourceDeviceID: AudioObjectID, destinationDeviceID: AudioObjectID, connectionType: AudioConnectionType) {
        self.timestamp = Date()
        self.sourceDeviceID = sourceDeviceID
        self.destinationDeviceID = destinationDeviceID
        self.connectionType = connectionType
    }
    
    func execute() async throws {
        // Implementation would remove connection
        // audioEngineManager.removeConnection(from: sourceDeviceID, to: destinationDeviceID)
    }
    
    func undo() async throws {
        // Implementation would restore connection
        // audioEngineManager.createConnection(from: sourceDeviceID, to: destinationDeviceID, type: connectionType)
    }
}

struct MoveNodeAction: UndoableAction {
    var actionType: ActionType { .moveNode }
    let timestamp: Date
    let nodeID: UUID
    let fromPosition: CGPoint
    let toPosition: CGPoint
    
    var description: String {
        "Move node from (\(Int(fromPosition.x)), \(Int(fromPosition.y))) to (\(Int(toPosition.x)), \(Int(toPosition.y)))"
    }
    
    init(nodeID: UUID, fromPosition: CGPoint, toPosition: CGPoint) {
        self.timestamp = Date()
        self.nodeID = nodeID
        self.fromPosition = fromPosition
        self.toPosition = toPosition
    }
    
    func execute() async throws {
        // Implementation would move node to new position
        // canvasManager.moveNode(nodeID, to: toPosition)
    }
    
    func undo() async throws {
        // Implementation would move node back to original position
        // canvasManager.moveNode(nodeID, to: fromPosition)
    }
}

struct LoadPresetAction: UndoableAction {
    var actionType: ActionType { .loadPreset }
    let timestamp: Date
    let presetName: String
    let previousState: [AudioNode]
    let newState: [AudioNode]
    
    var description: String {
        "Load preset '\(presetName)'"
    }
    
    init(presetName: String, previousState: [AudioNode], newState: [AudioNode]) {
        self.timestamp = Date()
        self.presetName = presetName
        self.previousState = previousState
        self.newState = newState
    }
    
    func execute() async throws {
        // Implementation would apply new preset state
        // canvasManager.loadPreset(newState)
    }
    
    func undo() async throws {
        // Implementation would restore previous state
        // canvasManager.loadPreset(previousState)
    }
}

// MARK: - Undo/Redo Manager

@MainActor
@Observable
class UndoRedoManager {
    private let logger = Logger(subsystem: "com.wavyaudio.core", category: "UndoRedo")
    
    // Action history
    private(set) var undoStack: [any UndoableAction] = []
    private(set) var redoStack: [any UndoableAction] = []
    
    // Configuration
    private let maxHistorySize: Int = 100
    private let maxHistoryAge: TimeInterval = 3600 // 1 hour
    
    // State
    private(set) var isUndoing = false
    private(set) var isRedoing = false
    private(set) var canUndo = false
    private(set) var canRedo = false
    
    // Auto-save state
    @ObservationIgnored @AppStorage("undoRedoHistory") private var historyData: Data?
    @ObservationIgnored private var autoSaveTimer: Timer?
    
    init() {
        loadHistory()
        startAutoSave()
        updateState()
    }
    
    deinit {
        // Note: deinit cannot be MainActor isolated
        // Timer cleanup handled through MainActor methods
    }
    
    // MARK: - Public API
    
    func executeAction(_ action: any UndoableAction) async throws {
        logger.info("Executing action: \(action.description)")
        
        do {
            try await action.execute()
            
            // Add to undo stack
            undoStack.append(action)
            
            // Clear redo stack when new action is performed
            redoStack.removeAll()
            
            // Maintain history size limits
            trimHistory()
            updateState()
            
        } catch {
            logger.error("Failed to execute action: \(error.localizedDescription)")
            throw error
        }
    }
    
    func undo() async throws {
        guard canUndo, let action = undoStack.last else {
            logger.warning("No action to undo")
            return
        }
        
        logger.info("Undoing action: \(action.description)")
        isUndoing = true
        
        do {
            try await action.undo()
            
            // Move action from undo to redo stack
            undoStack.removeLast()
            redoStack.append(action)
            
            updateState()
            
        } catch {
            logger.error("Failed to undo action: \(error.localizedDescription)")
            throw error
        }
        
        isUndoing = false
    }
    
    func redo() async throws {
        guard canRedo, let action = redoStack.last else {
            logger.warning("No action to redo")
            return
        }
        
        logger.info("Redoing action: \(action.description)")
        isRedoing = true
        
        do {
            try await action.execute()
            
            // Move action from redo to undo stack
            redoStack.removeLast()
            undoStack.append(action)
            
            updateState()
            
        } catch {
            logger.error("Failed to redo action: \(error.localizedDescription)")
            throw error
        }
        
        isRedoing = false
    }
    
    func clear() {
        logger.info("Clearing undo/redo history")
        undoStack.removeAll()
        redoStack.removeAll()
        stopAutoSave()
        updateState()
        saveHistory()
        startAutoSave()
    }
    
    // MARK: - History Management
    
    private func trimHistory() {
        // Remove old actions based on age
        let cutoffTime = Date().addingTimeInterval(-maxHistoryAge)
        undoStack.removeAll { $0.timestamp < cutoffTime }
        redoStack.removeAll { $0.timestamp < cutoffTime }
        
        // Limit stack size
        if undoStack.count > maxHistorySize {
            undoStack.removeFirst(undoStack.count - maxHistorySize)
        }
        
        if redoStack.count > maxHistorySize {
            redoStack.removeFirst(redoStack.count - maxHistorySize)
        }
    }
    
    private func updateState() {
        canUndo = !undoStack.isEmpty && !isRedoing
        canRedo = !redoStack.isEmpty && !isUndoing
    }
    
    // MARK: - Persistence
    
    private func saveHistory() {
        do {
            // Create a simplified version for persistence
            let undoDescriptions = undoStack.map { ["type": $0.actionType.rawValue, "description": $0.description, "timestamp": $0.timestamp] }
            let redoDescriptions = redoStack.map { ["type": $0.actionType.rawValue, "description": $0.description, "timestamp": $0.timestamp] }
            
            let historyDict: [String: Any] = [
                "undoStack": undoDescriptions,
                "redoStack": redoDescriptions,
                "savedAt": Date()
            ]
            
            let data = try JSONSerialization.data(withJSONObject: historyDict)
            historyData = data
            
        } catch {
            logger.error("Failed to save history: \(error.localizedDescription)")
        }
    }
    
    private func loadHistory() {
        guard let data = historyData else { return }
        
        do {
            let historyDict = try JSONSerialization.jsonObject(with: data) as? [String: Any]
            // For now, just log that we have saved history
            // Full restoration would require serializing actual actions
            if let savedAt = historyDict?["savedAt"] as? Date {
                logger.info("Found saved history from \(savedAt)")
            }
        } catch {
            logger.error("Failed to load history: \(error.localizedDescription)")
        }
    }
    
    private func startAutoSave() {
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.saveHistory()
            }
        }
    }
    
    private func stopAutoSave() {
        autoSaveTimer?.invalidate()
        autoSaveTimer = nil
    }
    
    // MARK: - Convenience Methods
    
    func getRecentActions(limit: Int = 10) -> [any UndoableAction] {
        return Array(undoStack.suffix(limit).reversed())
    }
    
    func getActionHistory() -> (undo: [String], redo: [String]) {
        let undoDescriptions = undoStack.map { $0.description }
        let redoDescriptions = redoStack.map { $0.description }
        return (undoDescriptions, redoDescriptions)
    }
    
    var nextUndoAction: String? {
        undoStack.last?.description
    }
    
    var nextRedoAction: String? {
        redoStack.last?.description
    }
}

// MARK: - Keyboard Shortcuts Support

extension UndoRedoManager {
    
    func handleKeyboardShortcut(_ event: NSEvent) -> Bool {
        let modifiers = event.modifierFlags
        let keyCode = event.keyCode
        
        // Cmd+Z for undo
        if modifiers.contains(.command) && !modifiers.contains(.shift) && keyCode == 6 { // Z key
            Task {
                try? await undo()
            }
            return true
        }
        
        // Cmd+Shift+Z for redo
        if modifiers.contains([.command, .shift]) && keyCode == 6 { // Z key
            Task {
                try? await redo()
            }
            return true
        }
        
        return false
    }
}

// MARK: - SwiftUI Integration

struct UndoRedoControls: View {
    @Environment(UndoRedoManager.self) private var undoRedoManager
    
    var body: some View {
        HStack {
            Button(action: {
                Task {
                    try? await undoRedoManager.undo()
                }
            }) {
                Label("Undo", systemImage: "arrow.uturn.backward")
            }
            .disabled(!undoRedoManager.canUndo)
            .help(undoRedoManager.nextUndoAction ?? "Nothing to undo")
            .keyboardShortcut("z", modifiers: .command)
            
            Button(action: {
                Task {
                    try? await undoRedoManager.redo()
                }
            }) {
                Label("Redo", systemImage: "arrow.uturn.forward")
            }
            .disabled(!undoRedoManager.canRedo)
            .help(undoRedoManager.nextRedoAction ?? "Nothing to redo")
            .keyboardShortcut("z", modifiers: [.command, .shift])
        }
    }
}

struct UndoRedoHistoryView: View {
    @Environment(UndoRedoManager.self) private var undoRedoManager
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("Recent Actions")
                .font(.headline)
                .padding(.bottom, 4)
            
            let recentActions = undoRedoManager.getRecentActions()
            
            if recentActions.isEmpty {
                Text("No actions performed")
                    .foregroundStyle(.secondary)
                    .italic()
            } else {
                ForEach(Array(recentActions.enumerated()), id: \.offset) { index, action in
                    HStack {
                        Image(systemName: actionIcon(for: action.actionType))
                            .foregroundStyle(.secondary)
                        
                        VStack(alignment: .leading, spacing: 2) {
                            Text(action.description)
                                .font(.caption)
                            
                            Text(action.timestamp, style: .relative)
                                .font(.caption2)
                                .foregroundStyle(.tertiary)
                        }
                        
                        Spacer()
                    }
                    .padding(.vertical, 2)
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 8))
    }
    
    private func actionIcon(for actionType: ActionType) -> String {
        switch actionType {
        case .createConnection: return "link"
        case .deleteConnection: return "link.slash"
        case .moveNode: return "move.3d"
        case .createNode: return "plus.circle"
        case .deleteNode: return "minus.circle"
        case .loadPreset: return "folder"
        case .savePreset: return "folder.badge.plus"
        case .changeSettings: return "gear"
        case .autoLayout: return "rectangle.3.group"
        case .clearAll: return "trash"
        }
    }
}