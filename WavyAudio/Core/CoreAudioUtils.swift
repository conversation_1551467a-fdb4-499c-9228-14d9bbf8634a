//
// CoreAudioUtils.swift - Core Audio Utility Functions
// WWDC Compliance Reference:
// - WWDC24-10178: Core Audio best practices (Property queries, error handling)
// - WWDC23-10152: Advanced Core Audio (Device enumeration, property management)
// - WWDC21-10190: DriverKit Audio (HAL integration patterns)
// - WWDC25-268: Swift 6 concurrency (Thread-safe Core Audio operations)
//
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --query "Core Audio property management thread safety"
//

import Foundation
import CoreAudio
import AudioToolbox

// MARK: - Core Audio Utilities

/// Thread-safe Core Audio utility functions following WWDC best practices
/// Implements patterns from WWDC24-10178 for safe property queries and error handling
actor CoreAudioUtils {
    
    // MARK: - Device Enumeration
    
    /// Retrieves all available audio devices using Core Audio HAL
    /// Thread-safe implementation following WWDC25-268 concurrency guidelines
    static func getAllAudioDevices() async -> [AudioDeviceID] {
        return await withCheckedContinuation { continuation in
            var propertyAddress = AudioObjectPropertyAddress(
                mSelector: kAudioHardwarePropertyDevices,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain
            )
            
            var propertySize: UInt32 = 0
            var status = AudioObjectGetPropertyDataSize(
                AudioObjectID(kAudioObjectSystemObject),
                &propertyAddress,
                0,
                nil,
                &propertySize
            )
            
            guard status == noErr else {
                continuation.resume(returning: [])
                return
            }
            
            let deviceCount = Int(propertySize) / MemoryLayout<AudioDeviceID>.size
            var deviceIDs = Array<AudioDeviceID>(repeating: 0, count: deviceCount)
            
            status = AudioObjectGetPropertyData(
                AudioObjectID(kAudioObjectSystemObject),
                &propertyAddress,
                0,
                nil,
                &propertySize,
                &deviceIDs
            )
            
            guard status == noErr else {
                continuation.resume(returning: [])
                return
            }
            
            continuation.resume(returning: deviceIDs)
        }
    }
    
    // MARK: - Device Properties
    
    /// Safely retrieves device name with proper error handling
    /// Follows WWDC24-10178 property query patterns
    static func getDeviceName(for deviceID: AudioDeviceID) async -> String {
        return await withCheckedContinuation { continuation in
            var propertyAddress = AudioObjectPropertyAddress(
                mSelector: kAudioDevicePropertyDeviceNameCFString,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain
            )
            
            var propertySize: UInt32 = 0
            var status = AudioObjectGetPropertyDataSize(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize
            )
            
            guard status == noErr else {
                continuation.resume(returning: "Unknown Device")
                return
            }
            
            var deviceName: CFString?
            status = AudioObjectGetPropertyData(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize,
                &deviceName
            )
            
            guard status == noErr, let name = deviceName else {
                continuation.resume(returning: "Unknown Device")
                return
            }
            
            continuation.resume(returning: name as String)
        }
    }
    
    /// Retrieves device manufacturer using Core Audio properties
    static func getDeviceManufacturer(for deviceID: AudioDeviceID) async -> String {
        return await withCheckedContinuation { continuation in
            var propertyAddress = AudioObjectPropertyAddress(
                mSelector: kAudioDevicePropertyDeviceManufacturerCFString,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain
            )
            
            var propertySize: UInt32 = 0
            var status = AudioObjectGetPropertyDataSize(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize
            )
            
            guard status == noErr else {
                continuation.resume(returning: "Unknown")
                return
            }
            
            var manufacturer: CFString?
            status = AudioObjectGetPropertyData(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize,
                &manufacturer
            )
            
            guard status == noErr, let mfg = manufacturer else {
                continuation.resume(returning: "Unknown")
                return
            }
            
            continuation.resume(returning: mfg as String)
        }
    }
    
    /// Gets channel count for a specific scope (input/output)
    static func getChannelCount(for deviceID: AudioDeviceID, scope: AudioObjectPropertyScope) async -> Int {
        return await withCheckedContinuation { continuation in
            var propertyAddress = AudioObjectPropertyAddress(
                mSelector: kAudioDevicePropertyStreamConfiguration,
                mScope: scope,
                mElement: kAudioObjectPropertyElementMain
            )
            
            var propertySize: UInt32 = 0
            var status = AudioObjectGetPropertyDataSize(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize
            )
            
            guard status == noErr else {
                continuation.resume(returning: 0)
                return
            }
            
            let bufferListPtr = UnsafeMutablePointer<AudioBufferList>.allocate(capacity: 1)
            defer { bufferListPtr.deallocate() }
            
            status = AudioObjectGetPropertyData(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize,
                bufferListPtr
            )
            
            guard status == noErr else {
                continuation.resume(returning: 0)
                return
            }
            
            let bufferList = bufferListPtr.pointee
            var totalChannels = 0
            
            for i in 0..<Int(bufferList.mNumberBuffers) {
                let buffer = withUnsafeBytes(of: bufferList) { bytes in
                    bytes.bindMemory(to: AudioBuffer.self)[i + 1]
                }
                totalChannels += Int(buffer.mNumberChannels)
            }
            
            continuation.resume(returning: totalChannels)
        }
    }
    
    /// Retrieves current sample rate for device
    static func getSampleRate(for deviceID: AudioDeviceID) async -> Float64 {
        return await withCheckedContinuation { continuation in
            var propertyAddress = AudioObjectPropertyAddress(
                mSelector: kAudioDevicePropertyNominalSampleRate,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain
            )
            
            var propertySize: UInt32 = UInt32(MemoryLayout<Float64>.size)
            var sampleRate: Float64 = 0.0
            
            let status = AudioObjectGetPropertyData(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize,
                &sampleRate
            )
            
            guard status == noErr else {
                continuation.resume(returning: 44100.0) // Default fallback
                return
            }
            
            continuation.resume(returning: sampleRate)
        }
    }
    
    // MARK: - Device Classification
    
    /// Determines if device is virtual (software-based)
    static func isVirtualDevice(_ deviceID: AudioDeviceID) async -> Bool {
        return await withCheckedContinuation { continuation in
            var propertyAddress = AudioObjectPropertyAddress(
                mSelector: kAudioDevicePropertyTransportType,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain
            )
            
            var propertySize: UInt32 = UInt32(MemoryLayout<UInt32>.size)
            var transportType: UInt32 = 0
            
            let status = AudioObjectGetPropertyData(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize,
                &transportType
            )
            
            guard status == noErr else {
                continuation.resume(returning: false)
                return
            }
            
            // Virtual devices typically use kAudioDeviceTransportTypeVirtual
            continuation.resume(returning: transportType == kAudioDeviceTransportTypeVirtual)
        }
    }
    
    /// Checks if device is an aggregate device
    static func isAggregateDevice(_ deviceID: AudioDeviceID) async -> Bool {
        return await withCheckedContinuation { continuation in
            var propertyAddress = AudioObjectPropertyAddress(
                mSelector: kAudioDevicePropertyDeviceCanBeDefaultDevice,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain
            )
            
            // Aggregate devices have specific properties we can check
            // This is a simplified check - in practice, you might need more sophisticated detection
            var propertySize: UInt32 = UInt32(MemoryLayout<UInt32>.size)
            var canBeDefault: UInt32 = 0
            
            let _ = AudioObjectGetPropertyData(
                deviceID,
                &propertyAddress,
                0,
                nil,
                &propertySize,
                &canBeDefault
            )
            
            // For now, return false as aggregate detection requires more complex logic
            continuation.resume(returning: false)
        }
    }
    
    // MARK: - Factory Method
    
    /// Creates an AudioDevice from a Core Audio device ID
    /// Implements async/await pattern from WWDC25-268 for concurrent property fetching
    static func createAudioDevice(from deviceID: AudioDeviceID) async -> AudioDevice {
        async let name = getDeviceName(for: deviceID)
        async let manufacturer = getDeviceManufacturer(for: deviceID)
        async let inputChannels = getChannelCount(for: deviceID, scope: kAudioDevicePropertyScopeInput)
        async let outputChannels = getChannelCount(for: deviceID, scope: kAudioDevicePropertyScopeOutput)
        async let sampleRate = getSampleRate(for: deviceID)
        async let isVirtual = isVirtualDevice(deviceID)
        async let isAggregate = isAggregateDevice(deviceID)
        
        return await AudioDevice(
            deviceID: deviceID,
            name: name,
            manufacturer: manufacturer,
            inputChannels: inputChannels,
            outputChannels: outputChannels,
            sampleRate: sampleRate,
            isVirtual: isVirtual,
            isAggregate: isAggregate
        )
    }
}

// MARK: - Error Handling

/// Core Audio specific errors following WWDC best practices
enum CoreAudioError: Error, LocalizedError {
    case deviceNotFound
    case propertyNotFound
    case invalidPropertySize
    case permissionDenied
    case hardwareError(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .deviceNotFound:
            return "Audio device not found"
        case .propertyNotFound:
            return "Audio device property not available"
        case .invalidPropertySize:
            return "Invalid property data size"
        case .permissionDenied:
            return "Permission denied to access audio device"
        case .hardwareError(let status):
            return "Core Audio hardware error: \(status)"
        }
    }
}