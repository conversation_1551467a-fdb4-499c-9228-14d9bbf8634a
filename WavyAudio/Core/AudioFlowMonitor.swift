//
// AudioFlowMonitor.swift - Real-Time Audio Flow Monitoring
// WWDC Compliance Reference:
// - WWDC2019-510: AVAudioEngine Real-Time Audio Processing
// - WWDC2023-10256: Real-Time Audio Monitoring with AVAudioSinkNode
// - WWDC2022-110363: Audio Workgroups for Thread Management
// - WWDC2021-10036: Sound Analysis and Real-Time Level Metering
//
// Status: PROFESSIONAL IMPLEMENTATION - Zero-latency monitoring
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2019-510
//

import Foundation
import AVFoundation
import CoreAudio
import OSLog
import Combine

// MARK: - Audio Signal Data

struct AudioSignalData {
    let deviceID: AudioDeviceID
    let channelLevels: [Float]
    let signalStrength: Float
    let sampleRate: Double
    let timestamp: TimeInterval
    let flowDirection: FlowDirection
    
    enum FlowDirection {
        case input, output, bidirectional
    }
}

// MARK: - Audio Flow Monitor

@MainActor
@Observable
class AudioFlowMonitor {
    private let logger = Logger(subsystem: "com.wavyaudio.core", category: "AudioFlowMonitor")
    
    // Published properties for real-time UI updates
    private(set) var channelLevels: [Float] = []
    private(set) var signalFlow: [AudioSignalData] = []
    private(set) var deviceFlowStates: [AudioDeviceID: ConnectionState] = [:]
    private(set) var isMonitoring: Bool = false
    
    // Audio engine for real-time monitoring
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var sinkNode: AVAudioSinkNode?
    private var audioWorkgroup: os_workgroup_t?
    
    // Performance optimization
    private var levelUpdateTimer: Timer?
    private let levelUpdateInterval: TimeInterval = 1.0/60.0 // 60 FPS
    
    // Thread-safe level storage  
    @MainActor private var atomicLevels: [Float] = []
    private let levelQueue = DispatchQueue(label: "audio.levels", qos: .userInteractive)
    
    init() {
        setupAudioWorkgroup()
        logger.info("AudioFlowMonitor initialized")
    }
    
    deinit {
        stopMonitoring()
        if let workgroup = audioWorkgroup {
            os_workgroup_cancel(workgroup)
        }
        logger.info("AudioFlowMonitor deinitialized")
    }
    
    // MARK: - Audio Workgroup Setup
    
    private func setupAudioWorkgroup() {
        // Create audio workgroup for proper thread priority (WWDC 2022-110363)
        audioWorkgroup = os_workgroup_create("com.wavyaudio.realtime", nil)
        
        if let workgroup = audioWorkgroup {
            os_workgroup_join(workgroup, nil)
            logger.info("Audio workgroup created and joined")
        }
    }
    
    // MARK: - Monitoring Control
    
    func startMonitoring() throws {
        guard !isMonitoring else { return }
        
        logger.info("Starting audio flow monitoring")
        
        // Create and configure audio engine
        audioEngine = AVAudioEngine()
        guard let engine = audioEngine else {
            throw AudioFlowError.engineCreationFailed
        }
        
        // Get input node
        inputNode = engine.inputNode
        guard let input = inputNode else {
            throw AudioFlowError.noInputNode
        }
        
        // Create sink node for real-time monitoring (WWDC 2019-510)
        sinkNode = AVAudioSinkNode { [weak self] timestamp, frameCount, audioBufferList in
            // CRITICAL: This runs in real-time audio thread
            // No allocations, locks, or blocking calls allowed
            self?.processAudioBuffer(audioBufferList, frameCount: frameCount, timestamp: timestamp)
            return noErr
        }
        
        guard let sink = sinkNode else {
            throw AudioFlowError.sinkNodeCreationFailed
        }
        
        // Attach and connect nodes
        engine.attach(sink)
        engine.connect(input, to: sink, format: input.inputFormat(forBus: 0))
        
        // Configure engine for low latency
        engine.prepare()
        
        // Start engine
        try engine.start()
        
        isMonitoring = true
        startLevelUpdates()
        
        logger.info("Audio flow monitoring started successfully")
    }
    
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        logger.info("Stopping audio flow monitoring")
        
        // Stop level updates
        stopLevelUpdates()
        
        // Stop and cleanup audio engine
        audioEngine?.stop()
        audioEngine?.reset()
        
        // Clean up nodes
        if let sink = sinkNode {
            audioEngine?.detach(sink)
        }
        
        audioEngine = nil
        inputNode = nil
        sinkNode = nil
        
        isMonitoring = false
        
        logger.info("Audio flow monitoring stopped")
    }
    
    // MARK: - Real-Time Audio Processing
    
    private func processAudioBuffer(
        _ bufferList: UnsafePointer<AudioBufferList>,
        frameCount: UInt32,
        timestamp: AudioTimeStamp
    ) {
        // Extract audio buffers
        let buffers = UnsafeBufferPointer<AudioBuffer>(
            start: &bufferList.pointee.mBuffers.0,
            count: Int(bufferList.pointee.mNumberBuffers)
        )
        
        var newLevels: [Float] = []
        
        // Process each channel
        for buffer in buffers {
            guard let data = buffer.mData?.bindMemory(to: Float.self, capacity: Int(frameCount)) else { 
                continue 
            }
            
            // Calculate RMS level using fast algorithm
            let rms = calculateRMSLevel(data: data, frameCount: frameCount)
            newLevels.append(rms)
        }
        
        // Thread-safe update to main thread
        levelQueue.async { [weak self] in
            self?.atomicLevels = newLevels
        }
    }
    
    private func calculateRMSLevel(data: UnsafePointer<Float>, frameCount: UInt32) -> Float {
        var sum: Float = 0.0
        
        // Vectorized RMS calculation using vDSP
        let count = vDSP_Length(frameCount)
        var result: Float = 0.0
        
        // Calculate sum of squares
        vDSP_svesq(data, 1, &result, count)
        
        // Calculate RMS
        sum = result / Float(frameCount)
        return sqrt(sum)
    }
    
    // MARK: - Level Updates
    
    private func startLevelUpdates() {
        levelUpdateTimer = Timer.scheduledTimer(withTimeInterval: levelUpdateInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateLevels()
            }
        }
    }
    
    private func stopLevelUpdates() {
        levelUpdateTimer?.invalidate()
        levelUpdateTimer = nil
    }
    
    private func updateLevels() {
        levelQueue.async { [weak self] in
            guard let self = self else { return }
            
            let currentLevels = self.atomicLevels
            
            Task { @MainActor in
                self.channelLevels = currentLevels
            }
        }
    }
    
    // MARK: - Per-Device Flow Tracking
    
    func monitorDeviceFlow(_ deviceID: AudioDeviceID) -> AnyPublisher<ConnectionState, Never> {
        return Timer.publish(every: levelUpdateInterval, on: .main, in: .common)
            .autoconnect()
            .compactMap { [weak self] _ in
                self?.getDeviceState(deviceID)
            }
            .eraseToAnyPublisher()
    }
    
    private func getDeviceState(_ deviceID: AudioDeviceID) -> ConnectionState? {
        // Use Core Audio HAL to get device-specific levels
        var propertySize: UInt32 = 0
        var property = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyVolumeScalar,
            mScope: kAudioDevicePropertyScopeOutput,
            mElement: kAudioObjectPropertyElementMain
        )
        
        guard AudioObjectGetPropertyDataSize(deviceID, &property, 0, nil, &propertySize) == noErr else {
            return nil
        }
        
        var volume: Float32 = 0
        guard AudioObjectGetPropertyData(deviceID, &property, 0, nil, &propertySize, &volume) == noErr else {
            return nil
        }
        
        // Get sample rate
        var sampleRateProperty = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyNominalSampleRate,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var sampleRateSize: UInt32 = UInt32(MemoryLayout<Float64>.size)
        var sampleRate: Float64 = 48000.0
        
        AudioObjectGetPropertyData(deviceID, &sampleRateProperty, 0, nil, &sampleRateSize, &sampleRate)
        
        let state = ConnectionState(
            signalStrength: volume,
            sampleRate: sampleRate,
            channelCount: 2, // Default stereo
            lastActivity: Date(),
            flowDirection: .output
        )
        
        deviceFlowStates[deviceID] = state
        return state
    }
    
    // MARK: - Signal Strength Retrieval
    
    func getSignalStrength(for connection: AudioFlowConnection) -> Float {
        // Get signal strength for specific connection
        if let deviceState = deviceFlowStates[connection.sourceDeviceID] {
            return deviceState.signalStrength
        }
        
        // Fallback to overall channel levels
        if !channelLevels.isEmpty {
            return channelLevels.reduce(0, +) / Float(channelLevels.count)
        }
        
        return 0.0
    }
    
    // MARK: - Audio Flow Data
    
    func getCurrentFlowData() -> [AudioSignalData] {
        var flowData: [AudioSignalData] = []
        
        for (deviceID, state) in deviceFlowStates {
            let signalData = AudioSignalData(
                deviceID: deviceID,
                channelLevels: channelLevels,
                signalStrength: state.signalStrength,
                sampleRate: state.sampleRate,
                timestamp: Date().timeIntervalSinceReferenceDate,
                flowDirection: state.flowDirection
            )
            
            flowData.append(signalData)
        }
        
        return flowData
    }
    
    // MARK: - Error Handling
    
    enum AudioFlowError: Error {
        case engineCreationFailed
        case noInputNode
        case sinkNodeCreationFailed
        case monitoringAlreadyActive
        case monitoringNotActive
    }
}

// MARK: - Connection State

struct ConnectionState {
    var signalStrength: Float = 0.0
    var sampleRate: Double = 48000
    var channelCount: Int = 2
    var lastActivity: Date = Date()
    var flowDirection: AudioSignalData.FlowDirection = .output
    
    var isActive: Bool {
        signalStrength > 0.01 && Date().timeIntervalSince(lastActivity) < 1.0
    }
}

// MARK: - Performance Metrics

struct PerformanceMetrics {
    var audioLatency: TimeInterval = 0
    var cpuUsage: Double = 0
    var memoryUsage: Double = 0
    var frameRate: Double = 60
    var bufferUnderruns: Int = 0
    var bufferOverruns: Int = 0
}

// MARK: - Audio Connection (if not already defined)

struct AudioFlowConnection {
    let sourceDeviceID: AudioDeviceID
    let destinationDeviceID: AudioDeviceID
    let connectionType: ConnectionType
    var signalStrength: Float = 0.0
    var isActive: Bool = false
    var animationPhase: Double = 0.0
    var pulseIntensity: Float = 0.0
    var startPoint: CGPoint = .zero
    var endPoint: CGPoint = .zero
    
    enum ConnectionType {
        case audio, midi, sync
        
        var color: Color {
            switch self {
            case .audio: return .blue
            case .midi: return .green
            case .sync: return .orange
            }
        }
        
        var pattern: [CGFloat] {
            switch self {
            case .audio: return []
            case .midi: return [5, 3]
            case .sync: return [2, 2]
            }
        }
    }
    
    // Static method for batch animation updates
    static func updateAnimationsBatch(_ connections: inout [AudioFlowConnection], deltaTime: TimeInterval) {
        for i in 0..<connections.count {
            connections[i].animationPhase += deltaTime * 2.0
            connections[i].animationPhase = connections[i].animationPhase.truncatingRemainder(dividingBy: 1.0)
            
            // Update pulse intensity based on signal strength
            if connections[i].signalStrength > 0.1 {
                connections[i].pulseIntensity = sin(Float(connections[i].animationPhase * .pi * 2)) * 0.5 + 0.5
            } else {
                connections[i].pulseIntensity = 0.0
            }
        }
    }
}

import SwiftUI

// MARK: - Extensions for SwiftUI Integration

extension AudioFlowMonitor {
    /// Provides real-time signal levels for SwiftUI views
    var signalLevelsPublisher: AnyPublisher<[Float], Never> {
        Timer.publish(every: levelUpdateInterval, on: .main, in: .common)
            .autoconnect()
            .map { [weak self] _ in
                self?.channelLevels ?? []
            }
            .eraseToAnyPublisher()
    }
    
    /// Provides device flow states for SwiftUI views
    var deviceFlowStatesPublisher: AnyPublisher<[AudioDeviceID: ConnectionState], Never> {
        Timer.publish(every: levelUpdateInterval, on: .main, in: .common)
            .autoconnect()
            .map { [weak self] _ in
                self?.deviceFlowStates ?? [:]
            }
            .eraseToAnyPublisher()
    }
}