import Foundation
import CoreAudio
import AVFoundation
import AppKit
import OSLog
import Combine
// TODO: Re-enable SimplyCoreAudio once package dependency is properly configured
// import SimplyCoreAudio

// MARK: - Device Information

struct AudioDeviceInfo {
    let deviceID: AudioDeviceID
    let name: String
    let manufacturer: String
    let inputChannels: Int
    let outputChannels: Int
    let sampleRate: Float64
    let isVirtual: Bool
    let isAggregate: Bool
    let isConnected: Bool
    let isRunning: Bool
}



// MARK: - Core Audio HAL Manager

@MainActor
class CoreAudioHALManager: ObservableObject {
    private let logger = Logger(subsystem: "com.wavyaudio.core", category: "HALManager")
    private var isInitialized = false
    private var deviceListeners: [AudioDeviceID: AudioObjectPropertyListenerProc] = [:]
    private var deviceListListenerAddress: AudioObjectPropertyAddress?
    private var deviceListListener: AudioObjectPropertyListenerProc?
    
    // Singleton pattern for HAL manager
    static let shared = CoreAudioHALManager()
    
    private init() {}
    
    // MARK: - Initialization
    
    func initialize() async throws {
        guard !isInitialized else { return }
        
        logger.info("Initializing Core Audio HAL Manager")
        
        // Request audio permissions
        let granted = await AVAudioApplication.requestRecordPermission()
        guard granted else {
            throw AudioEngineError.permissionDenied
        }
        
        // Initialize Core Audio HAL
        try initializeCoreAudioHAL()
        
        // Setup device change notifications
        try setupDeviceNotifications()
        
        isInitialized = true
        logger.info("Core Audio HAL Manager initialized successfully")
    }
    
    private func initializeCoreAudioHAL() throws {
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioHardwarePropertyRunLoop,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var runLoop = CFRunLoopGetCurrent()
        let status = AudioObjectSetPropertyData(
            AudioObjectID(kAudioObjectSystemObject),
            &address,
            0, nil,
            UInt32(MemoryLayout.size(ofValue: runLoop)),
            &runLoop
        )
        
        guard status == noErr else {
            logger.error("Failed to set Core Audio run loop: \(status)")
            throw AudioEngineError.halInitializationFailed
        }
    }
    
    private func setupDeviceNotifications() throws {
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioHardwarePropertyDevices,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let listener: AudioObjectPropertyListenerProc = { (objectID, numberAddresses, addresses, clientData) in
            // Handle device list changes
            Task { @MainActor in
                if let manager = clientData?.assumingMemoryBound(to: CoreAudioHALManager.self).pointee {
                    manager.handleDeviceListChange()
                }
            }
            return noErr
        }
        
        self.deviceListListenerAddress = address
        self.deviceListListener = listener

        let status = AudioObjectAddPropertyListener(
            AudioObjectID(kAudioObjectSystemObject),
            &address,
            listener,
            Unmanaged.passUnretained(self).toOpaque()
        )
        
        guard status == noErr else {
            logger.error("Failed to setup device notifications: \(status)")
            throw AudioEngineError.halInitializationFailed
        }
    }
    
    // MARK: - Device Enumeration
    
    func enumerateDevices() -> [AudioDeviceInfo] {
        guard isInitialized else {
            logger.warning("HAL Manager not initialized, returning empty device list")
            return []
        }
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioHardwarePropertyDevices,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var deviceIDs: [AudioDeviceID] = []
        var propertySize = UInt32(MemoryLayout<AudioDeviceID>.size * 64)
        
        let status = AudioObjectGetPropertyData(
            AudioObjectID(kAudioObjectSystemObject),
            &address, 0, nil,
            &propertySize, &deviceIDs
        )
        
        guard status == noErr else {
            logger.error("Failed to enumerate devices: \(status)")
            return []
        }
        
        let count = Int(propertySize) / MemoryLayout<AudioDeviceID>.size
        deviceIDs = Array(deviceIDs.prefix(count))
        
        return deviceIDs.compactMap { createDeviceInfo(from: $0) }
    }
    
    private func createDeviceInfo(from deviceID: AudioDeviceID) -> AudioDeviceInfo? {
        let name = getDeviceName(deviceID)
        let manufacturer = getDeviceManufacturer(deviceID)
        let sampleRate = getDeviceSampleRate(deviceID)
        let inputChannels = getDeviceChannelCount(deviceID, scope: kAudioObjectPropertyScopeInput)
        let outputChannels = getDeviceChannelCount(deviceID, scope: kAudioObjectPropertyScopeOutput)
        let isVirtual = isVirtualDevice(deviceID)
        let isAggregate = isAggregateDevice(deviceID)
        
        return AudioDeviceInfo(
            deviceID: deviceID,
            name: name,
            manufacturer: manufacturer,
            inputChannels: inputChannels,
            outputChannels: outputChannels,
            sampleRate: sampleRate,
            isVirtual: isVirtual,
            isAggregate: isAggregate,
            isConnected: true, // Assume connected if we can enumerate it
            isRunning: true    // Assume running if we can enumerate it
        )
    }
    
    // MARK: - Device Property Helpers
    
    private func getDeviceName(_ deviceID: AudioDeviceID) -> String {
        var name: CFString = "" as CFString
        var propertySize = UInt32(MemoryLayout<CFString>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioObjectPropertyName,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID, &address, 0, nil,
            &propertySize, &name
        )
        
        return status == noErr ? String(name) : "Unknown Device"
    }
    
    private func getDeviceManufacturer(_ deviceID: AudioDeviceID) -> String {
        var manufacturer: CFString = "" as CFString
        var propertySize = UInt32(MemoryLayout<CFString>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioObjectPropertyManufacturer,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID, &address, 0, nil,
            &propertySize, &manufacturer
        )
        
        return status == noErr ? String(manufacturer) : "Unknown"
    }
    
    private func getDeviceSampleRate(_ deviceID: AudioDeviceID) -> Float64 {
        var sampleRate: Float64 = 44100.0
        var propertySize = UInt32(MemoryLayout<Float64>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyNominalSampleRate,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        AudioObjectGetPropertyData(
            deviceID, &address, 0, nil,
            &propertySize, &sampleRate
        )
        
        return sampleRate
    }
    
    private func getDeviceChannelCount(_ deviceID: AudioDeviceID, scope: AudioObjectPropertyScope) -> Int {
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyStreamConfiguration,
            mScope: scope,
            mElement: kAudioObjectPropertyElementMain
        )
        
        // Get property size
        var propertySize: UInt32 = 0
        var status = AudioObjectGetPropertyDataSize(deviceID, &address, 0, nil, &propertySize)
        guard status == noErr else {
            logger.error("Failed to get stream configuration size for device \(deviceID): \(status)")
            return 0
        }
        
        // Allocate buffer list with proper alignment
        let bufferList = UnsafeMutablePointer<AudioBufferList>.allocate(capacity: Int(propertySize))
        defer { bufferList.deallocate() }
        
        // Initialize buffer list memory
        memset(bufferList, 0, Int(propertySize))
        
        // Get property data
        status = AudioObjectGetPropertyData(deviceID, &address, 0, nil, &propertySize, bufferList)
        guard status == noErr else {
            logger.error("Failed to get stream configuration for device \(deviceID): \(status)")
            return 0
        }
        
        // Safely access buffer list
        var channelCount = 0
        let bufferListPtr = UnsafeMutableAudioBufferListPointer(bufferList)
        
        for buffer in bufferListPtr {
            channelCount += Int(buffer.mNumberChannels)
        }
        
        return channelCount
    }
    
    private func isVirtualDevice(_ deviceID: AudioDeviceID) -> Bool {
        var transportType: UInt32 = 0
        var propertySize = UInt32(MemoryLayout<UInt32>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyTransportType,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID, &address, 0, nil,
            &propertySize, &transportType
        )
        
        guard status == noErr else {
            logger.warning("Failed to get transport type for device \(deviceID): \(status)")
            return false
        }
        
        return transportType == kAudioDeviceTransportTypeVirtual
    }
    
    private func isAggregateDevice(_ deviceID: AudioDeviceID) -> Bool {
        var transportType: UInt32 = 0
        var propertySize = UInt32(MemoryLayout<UInt32>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyTransportType,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID, &address, 0, nil,
            &propertySize, &transportType
        )
        
        guard status == noErr else {
            logger.warning("Failed to get transport type for device \(deviceID): \(status)")
            return false
        }
        
        return transportType == kAudioDeviceTransportTypeAggregate
    }

    // MARK: - Health Check
    
    func runHealthCheck() -> [SystemConflict] {
        var conflicts: [SystemConflict] = []

        if isIPhoneConnected() {
            conflicts.append(SystemConflict(
                type: .otherAudioApp,
                description: "An iPhone is connected via USB. This can sometimes cause audio instability. If you experience issues, try disconnecting the iPhone.",
                affectedDevices: [],
                severity: .warning
            ))
        }

        if !isArkDriverInstalled() {
            conflicts.append(SystemConflict(
                type: .arkDaemon,
                description: "Ark Audio Driver is not installed. Please install it to enable audio routing features.",
                affectedDevices: [],
                severity: .errorLevel
            ))
        }

        return conflicts
    }
    
    // MARK: - Device-Specific Conflict Detection
    
    func detectDeviceSpecificConflicts(deviceID: AudioDeviceID) -> [SystemConflict] {
        var conflicts: [SystemConflict] = []
        
        // Check for exclusive access conflicts
        if isDeviceInExclusiveUse(deviceID: deviceID) {
            conflicts.append(SystemConflict(
                type: .exclusiveAccess,
                description: "Device is currently in exclusive use by another application",
                affectedDevices: [deviceID],
                severity: .errorLevel
            ))
        }
        
        // Check for sample rate conflicts
        if let expectedSampleRate = getExpectedSampleRate(deviceID: deviceID),
           let actualSampleRate = getCurrentSampleRate(deviceID: deviceID),
           abs(expectedSampleRate - actualSampleRate) > 1.0 {
            conflicts.append(SystemConflict.sampleRateConflict(
                deviceID: deviceID,
                expectedRate: expectedSampleRate,
                actualRate: actualSampleRate
            ))
        }
        
        // Check for buffer size conflicts
        if hasBufferSizeConflict(deviceID: deviceID) {
            conflicts.append(SystemConflict(
                type: .bufferSizeConflict,
                description: "Device buffer size configuration conflicts with system requirements",
                affectedDevices: [deviceID],
                severity: .warning
            ))
        }
        
        return conflicts
    }
    
    private func isDeviceInExclusiveUse(deviceID: AudioDeviceID) -> Bool {
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyDeviceIsRunning,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var isRunning: UInt32 = 0
        var dataSize = UInt32(MemoryLayout<UInt32>.size)
        
        let status = AudioObjectGetPropertyData(deviceID, &propertyAddress, 0, nil, &dataSize, &isRunning)
        return status == noErr && isRunning != 0
    }
    
    private func getExpectedSampleRate(deviceID: AudioDeviceID) -> Double? {
        // Return the expected sample rate based on project configuration
        return 44100.0 // Common professional standard
    }
    
    private func getCurrentSampleRate(deviceID: AudioDeviceID) -> Double? {
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyNominalSampleRate,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var sampleRate: Float64 = 0
        var dataSize = UInt32(MemoryLayout<Float64>.size)
        
        let status = AudioObjectGetPropertyData(deviceID, &propertyAddress, 0, nil, &dataSize, &sampleRate)
        return status == noErr ? sampleRate : nil
    }
    
    private func hasBufferSizeConflict(deviceID: AudioDeviceID) -> Bool {
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyBufferFrameSize,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var bufferSize: UInt32 = 0
        var dataSize = UInt32(MemoryLayout<UInt32>.size)
        
        let status = AudioObjectGetPropertyData(deviceID, &propertyAddress, 0, nil, &dataSize, &bufferSize)
        
        // Check if buffer size is within acceptable range (64-2048 frames for professional audio)
        return status == noErr && (bufferSize < 64 || bufferSize > 2048)
    }

    private func isIPhoneConnected() -> Bool {
        let devices = enumerateDevices()
        return devices.contains { $0.manufacturer == "Apple Inc." && $0.name.contains("iPhone") }
    }

    private func isArkDriverInstalled() -> Bool {
        let devices = enumerateDevices()
        return devices.contains { $0.manufacturer == "Ark Audio" }
    }

    private func isAudioDevicePresent(manufacturer: String, nameContains: String? = nil) -> Bool {
        let devices = enumerateDevices()
        return devices.contains { device in
            device.manufacturer == manufacturer &&
            (nameContains == nil || device.name.contains(nameContains!))
        }
    }
    
    // MARK: - System Conflict Detection
    
    func detectSystemConflicts() -> [SystemConflict] {
        var conflicts: [SystemConflict] = []
        
        // Check for ARK daemon
        if detectARKDaemon() {
            conflicts.append(SystemConflict(
                type: .arkDaemon,
                description: "ARK daemon detected - audio routing may be affected",
                affectedDevices: [],
                severity: .warning
            ))
        }
        
        // Check for SoundSource
        if detectSoundSource() {
            conflicts.append(SystemConflict(
                type: .soundSource,
                description: "SoundSource detected - enabling compatibility mode",
                affectedDevices: [],
                severity: .info
            ))
        }
        
        return conflicts
    }
    
    func detectARKDaemon() -> Bool {
        // Check for ARK daemon process
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/bin/ps")
        task.arguments = ["-ax"]
        
        let pipe = Pipe()
        task.standardOutput = pipe
        
        do {
            try task.run()
            task.waitUntilExit()
            
            guard task.terminationStatus == 0 else {
                logger.error("ps command failed with status \(task.terminationStatus)")
                return false
            }
            
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            guard let output = String(data: data, encoding: .utf8) else {
                logger.error("Failed to decode ps command output")
                return false
            }
            
            return output.contains("ark") || output.contains("ARK")
        } catch {
            logger.error("Failed to check for ARK daemon: \(error)")
            return false
        }
    }
    
    func detectSoundSource() -> Bool {
        // Check for SoundSource application
        let workspace = NSWorkspace.shared
        let runningApps = workspace.runningApplications
        
        return runningApps.contains { app in
            guard let bundleIdentifier = app.bundleIdentifier,
                  let localizedName = app.localizedName else {
                return false
            }
            return bundleIdentifier.contains("SoundSource") ||
                   localizedName.contains("SoundSource")
        }
    }
    
    // MARK: - Device Change Notifications
    
    private func handleDeviceListChange() {
        logger.info("Audio device list changed")
        // Notify observers about device changes
        Task { @MainActor in
            NotificationCenter.default.post(name: .audioDeviceListChanged, object: nil)
        }
    }
    
    // MARK: - Cleanup
    
    deinit {
        // Perform synchronous cleanup to avoid Task capture issues
        // Remove property listeners synchronously
        if let address = deviceListListenerAddress, let listener = deviceListListener {
            var mutableAddress = address
            let status = AudioObjectRemovePropertyListener(
                AudioObjectID(kAudioObjectSystemObject),
                &mutableAddress,
                listener,
                Unmanaged.passUnretained(self).toOpaque()
            )
            if status != noErr {
                logger.error("Failed to remove device list listener during deinit: \(status)")
            }
        }
    }
    
    private func cleanup() {
        // Remove property listeners
        Task { @MainActor in
            if let address = deviceListListenerAddress, let listener = deviceListListener {
                var mutableAddress = address
                let status = AudioObjectRemovePropertyListener(
                    AudioObjectID(kAudioObjectSystemObject),
                    &mutableAddress,
                    listener,
                    Unmanaged.passUnretained(self).toOpaque()
                )
                if status != noErr {
                    logger.error("Failed to remove device list listener during cleanup: \(status)")
                }
            }
            
            isInitialized = false
            logger.info("Core Audio HAL Manager cleaned up")
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let audioDeviceListChanged = Notification.Name("audioDeviceListChanged")
}
