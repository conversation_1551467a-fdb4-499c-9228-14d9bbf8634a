import Foundation
import CoreAudio
import AVFoundation
import AppKit
import OSLog
import Combine
import os

// MARK: - Device Information

struct AudioDeviceInfo: Sendable {
    let deviceID: AudioDeviceID
    let name: String
    let manufacturer: String
    let inputChannels: Int
    let outputChannels: Int
    let sampleRate: Float64
    let isVirtual: Bool
    let isAggregate: Bool
    let isConnected: Bool
    let isRunning: Bool
    
    // Thread-safe initialization
    init(deviceID: AudioDeviceID, 
         name: String, 
         manufacturer: String, 
         inputChannels: Int, 
         outputChannels: Int, 
         sampleRate: Float64, 
         isVirtual: Bool, 
         isAggregate: Bool, 
         isConnected: Bool, 
         isRunning: Bool) {
        self.deviceID = deviceID
        self.name = name
        self.manufacturer = manufacturer
        self.inputChannels = inputChannels
        self.outputChannels = outputChannels
        self.sampleRate = sampleRate
        self.isVirtual = isVirtual
        self.isAggregate = isAggregate
        self.isConnected = isConnected
        self.isRunning = isRunning
    }
}

// MARK: - Core Audio HAL Manager

@MainActor
final class CoreAudioHALManager: @unchecked Sendable {
    // MARK: - Properties
    
    private let logger = Logger(subsystem: "com.wavyaudio.core", category: "CoreAudioHAL")
    private var isInitialized = false
    
    // Property listeners
    private var deviceListeners: [AudioDeviceID: (listener: AudioObjectPropertyListenerProc, context: UnsafeMutableRawPointer)] = [:]
    private var deviceListListener: AudioObjectPropertyListenerProc?
    private var deviceListListenerAddress: AudioObjectPropertyAddress?
    
    // Thread-safe access to devices
    private let deviceAccessQueue = DispatchQueue(
        label: "com.wavyaudio.core.deviceAccess",
        qos: .userInteractive,
        attributes: .concurrent
    )
    
    // Cached devices
    private var cachedDevices: [AudioDeviceID: AudioDeviceInfo] = [:]
    
    // MARK: - Initialization
    
    static let shared = CoreAudioHALManager()
    
    private init() {
        // Pre-allocate resources
        setupMemoryPools()
    }
    
    deinit {
        cleanup()
    }
    
    // MARK: - Public API
    
    func initialize() async throws {
        guard !isInitialized else { return }
        
        logger.info("Initializing Core Audio HAL Manager")
        
        // Request audio permissions
        let granted = await AVAudioApplication.requestRecordPermission()
        guard granted else {
            throw AudioEngineError.permissionDenied
        }
        
        // Initialize Core Audio HAL
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            deviceAccessQueue.async {
                do {
                    try self.initializeCoreAudioHAL()
                    try self.setupDeviceNotifications()
                    self.isInitialized = true
                    self.logger.info("Core Audio HAL Manager initialized successfully")
                    continuation.resume()
                } catch {
                    self.logger.error("Failed to initialize Core Audio HAL: \(error.localizedDescription)")
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    func enumerateDevices() -> [AudioDeviceInfo] {
        deviceAccessQueue.sync {
            var devices: [AudioDeviceInfo] = []
            var deviceIDs: [AudioDeviceID] = []
            var size = UInt32(MemoryLayout<AudioDeviceID>.size)
            
            // Get device count
            var address = AudioObjectPropertyAddress(
                mSelector: kAudioHardwarePropertyDevices,
                mScope: kAudioObjectPropertyScopeGlobal,
                mElement: kAudioObjectPropertyElementMain
            )
            
            var result = AudioObjectGetPropertyDataSize(
                AudioObjectID(kAudioObjectSystemObject),
                &address,
                0,
                nil,
                &size
            )
            
            guard result == noErr, size > 0 else {
                return []
            }
            
            // Get device IDs
            let count = Int(size) / MemoryLayout<AudioDeviceID>.size
            deviceIDs = [AudioDeviceID](repeating: 0, count: count)
            
            result = AudioObjectGetPropertyData(
                AudioObjectID(kAudioObjectSystemObject),
                &address,
                0,
                nil,
                &size,
                &deviceIDs
            )
            
            guard result == noErr else {
                return []
            }
            
            // Process devices
            for deviceID in deviceIDs {
                // Check cache first
                if let cachedDevice = self.cachedDevices[deviceID] {
                    devices.append(cachedDevice)
                    continue
                }
                
                // Create new device info
                if let deviceInfo = self.createDeviceInfo(from: deviceID) {
                    // Cache the result
                    self.cachedDevices[deviceID] = deviceInfo
                    devices.append(deviceInfo)
                }
            }
            
            return devices
        }
    }
    
    func detectSystemConflicts() async -> [SystemConflict] {
        var conflicts: [SystemConflict] = []
        
        // Check for ARK daemon
        if detectARKDaemon() {
            conflicts.append(SystemConflict(
                title: "ARK Daemon Detected",
                message: "ARK daemon detected - audio routing may be affected",
                severity: .warning
            ))
        }
        
        // Check for SoundSource
        if detectSoundSource() {
            conflicts.append(SystemConflict(
                title: "SoundSource Detected",
                message: "SoundSource detected - enabling compatibility mode",
                severity: .info
            ))
        }
        
        return conflicts
    }
    
    func detectDeviceSpecificConflicts(deviceID: AudioDeviceID) -> [SystemConflict] {
        var conflicts: [SystemConflict] = []
        
        // Check for exclusive access conflicts
        var isInUse: UInt32 = 0
        var size = UInt32(MemoryLayout<UInt32>.size)
        var propertyAddress = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyDeviceIsRunningSomewhere,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID,
            &propertyAddress,
            0,
            nil,
            &size,
            &isInUse
        )
        
        if status == noErr && isInUse != 0 {
            conflicts.append(SystemConflict(
                title: "Device In Use",
                message: "Device is currently in use by another application",
                severity: .warning
            ))
        }
        
        // Check for sample rate conflicts
        var nominalSampleRate: Float64 = 0
        size = UInt32(MemoryLayout<Float64>.size)
        propertyAddress.mSelector = kAudioDevicePropertyNominalSampleRate
        
        let sampleRateStatus = AudioObjectGetPropertyData(
            deviceID,
            &propertyAddress,
            0,
            nil,
            &size,
            &nominalSampleRate
        )
        
        if sampleRateStatus == noErr {
            let preferredSampleRate: Float64 = 44100.0
            if abs(nominalSampleRate - preferredSampleRate) > 0.1 {
                conflicts.append(SystemConflict(
                    title: "Sample Rate Mismatch",
                    message: "Device sample rate (\(Int(nominalSampleRate))Hz) differs from preferred (44.1kHz)",
                    severity: .info
                ))
            }
        }
        
        return conflicts
    }
    
    // MARK: - Memory Management
    
    private func setupMemoryPools() {
        // Pre-allocate memory pools for audio processing
        // This is a placeholder - actual implementation would use OSAllocationPool or similar
        logger.debug("Setting up memory pools")
    }
    
    // Changed from private to public to fix compilation error
    func cleanup() {
        // Remove all property listeners
        for (deviceID, listenerInfo) in deviceListeners {
            var address = AudioObjectPropertyAddress(
                mSelector: kAudioObjectPropertySelectorWildcard,
                mScope: kAudioObjectPropertyScopeWildcard,
                mElement: kAudioObjectPropertyElementWildcard
            )
            
            AudioObjectRemovePropertyListener(
                deviceID,
                &address,
                listenerInfo.listener,
                listenerInfo.context
            )
            
            // Free the context memory
            listenerInfo.context.deallocate()
        }
        
        deviceListeners.removeAll()
        
        // Clean up device list listener
        if let listener = deviceListListener,
           let address = deviceListListenerAddress {
            var mutableAddress = address
            AudioObjectRemovePropertyListener(
                AudioObjectID(kAudioObjectSystemObject),
                &mutableAddress,
                listener,
                Unmanaged.passUnretained(self).toOpaque()
            )
            
            deviceListListener = nil
            deviceListListenerAddress = nil
        }
        
        // Clear cached devices
        cachedDevices.removeAll()
        
        logger.info("Core Audio HAL Manager cleaned up")
    }
    
    // MARK: - Private Methods
    
    private func initializeCoreAudioHAL() throws {
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioHardwarePropertyRunLoop,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        // Set the run loop for Core Audio callbacks
        var runLoop = CFRunLoopGetCurrent()
        let status = AudioObjectSetPropertyData(
            AudioObjectID(kAudioObjectSystemObject),
            &address,
            0,
            nil,
            UInt32(MemoryLayout<CFRunLoop>.size),
            &runLoop
        )
        
        guard status == noErr else {
            throw AudioEngineError.halInitializationFailed
        }
    }
    
    private func setupDeviceNotifications() throws {
        let context = Unmanaged.passUnretained(self).toOpaque()
        
        let listener: AudioObjectPropertyListenerProc = { (objectID, numberAddresses, addresses, clientData) in
            guard let clientData = clientData else { return noErr }
            let manager = Unmanaged<CoreAudioHALManager>.fromOpaque(clientData).takeUnretainedValue()
            
            // Process device changes on a background queue
            DispatchQueue.global(qos: .userInitiated).async {
                manager.handleDeviceListChange()
            }
            
            return noErr
        }
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioHardwarePropertyDevices,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectAddPropertyListener(
            AudioObjectID(kAudioObjectSystemObject),
            &address,
            listener,
            context
        )
        
        guard status == noErr else {
            throw AudioEngineError.deviceListenerFailed
        }
        
        // Store the listener and context for cleanup
        deviceListListener = listener
        deviceListListenerAddress = address
    }
    
    private func handleDeviceListChange() {
        logger.info("Audio device list changed")
        // Notify observers about device changes
        Task { @MainActor in
            NotificationCenter.default.post(name: .audioDeviceListChanged, object: nil)
        }
    }
    
    private func createDeviceInfo(from deviceID: AudioDeviceID) -> AudioDeviceInfo? {
        // Get device name
        let name = getDeviceName(deviceID)
        
        // Get manufacturer
        let manufacturer = getDeviceManufacturer(deviceID)
        
        // Get input/output channels
        let inputChannels = getChannelCount(for: deviceID, scope: kAudioDevicePropertyScopeInput)
        let outputChannels = getChannelCount(for: deviceID, scope: kAudioDevicePropertyScopeOutput)
        
        // Get sample rate
        let sampleRate = getDeviceSampleRate(deviceID)
        
        // Check if device is running
        let isRunning = isDeviceRunning(deviceID)
        
        // Create device info
        return AudioDeviceInfo(
            deviceID: deviceID,
            name: name,
            manufacturer: manufacturer,
            inputChannels: inputChannels,
            outputChannels: outputChannels,
            sampleRate: sampleRate,
            isVirtual: isVirtualDevice(deviceID),
            isAggregate: isAggregateDevice(deviceID),
            isConnected: isDeviceConnected(deviceID),
            isRunning: isRunning
        )
    }
    
    private func getDeviceName(_ deviceID: AudioDeviceID) -> String {
        var name: CFString = "" as CFString
        var propertySize = UInt32(MemoryLayout<CFString>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyDeviceName,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID,
            &address,
            0,
            nil,
            &propertySize,
            &name
        )
        
        return status == noErr ? String(name) : "Unknown Device"
    }
    
    private func getDeviceManufacturer(_ deviceID: AudioDeviceID) -> String {
        var manufacturer: CFString = "" as CFString
        var propertySize = UInt32(MemoryLayout<CFString>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyDeviceManufacturer,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID,
            &address,
            0,
            nil,
            &propertySize,
            &manufacturer
        )
        
        return status == noErr ? String(manufacturer) : "Unknown"
    }
    
    private func getDeviceSampleRate(_ deviceID: AudioDeviceID) -> Float64 {
        var sampleRate: Float64 = 44100.0
        var propertySize = UInt32(MemoryLayout<Float64>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyNominalSampleRate,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        AudioObjectGetPropertyData(
            deviceID,
            &address,
            0,
            nil,
            &propertySize,
            &sampleRate
        )
        
        return sampleRate
    }
    
    private func isDeviceRunning(_ deviceID: AudioDeviceID) -> Bool {
        var isRunning: UInt32 = 0
        var propertySize = UInt32(MemoryLayout<UInt32>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyDeviceIsRunningSomewhere,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID,
            &address,
            0,
            nil,
            &propertySize,
            &isRunning
        )
        
        return (status == noErr) && (isRunning != 0)
    }
    
    private func getChannelCount(for deviceID: AudioDeviceID, scope: AudioObjectPropertyScope) -> Int {
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyStreamConfiguration,
            mScope: scope,
            mElement: kAudioObjectPropertyElementMain
        )
        
        var propertySize: UInt32 = 0
        var status = AudioObjectGetPropertyDataSize(
            deviceID,
            &address,
            0,
            nil,
            &propertySize
        )
        
        guard status == noErr, propertySize > 0 else {
            return 0
        }
        
        let bufferList = UnsafeMutablePointer<AudioBufferList>.allocate(capacity: 1)
        defer { bufferList.deallocate() }
        
        status = AudioObjectGetPropertyData(
            deviceID,
            &address,
            0,
            nil,
            &propertySize,
            bufferList
        )
        
        guard status == noErr else {
            return 0
        }
        
        var channelCount = 0
        let buffers = UnsafeBufferPointer<AudioBuffer>(
            start: &bufferList.pointee.mBuffers,
            count: Int(bufferList.pointee.mNumberBuffers)
        )
        
        for buffer in buffers {
            channelCount += Int(buffer.mNumberChannels)
        }
        
        return channelCount
    }
    
    private func isVirtualDevice(_ deviceID: AudioDeviceID) -> Bool {
        var transportType: UInt32 = 0
        var propertySize = UInt32(MemoryLayout<UInt32>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyTransportType,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID,
            &address,
            0,
            nil,
            &propertySize,
            &transportType
        )
        
        guard status == noErr else {
            return false
        }
        
        return transportType == kAudioDeviceTransportTypeVirtual
    }
    
    private func isAggregateDevice(_ deviceID: AudioDeviceID) -> Bool {
        var transportType: UInt32 = 0
        var propertySize = UInt32(MemoryLayout<UInt32>.size)
        
        var address = AudioObjectPropertyAddress(
            mSelector: kAudioDevicePropertyTransportType,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMain
        )
        
        let status = AudioObjectGetPropertyData(
            deviceID,
            &address,
            0,
            nil,
            &propertySize,
            &transportType
        )
        
        guard status == noErr else {
            return false
        }
        
        return transportType == kAudioDeviceTransportTypeAggregate
    }
    
    private func isDeviceConnected(_ deviceID: AudioDeviceID) -> Bool {
        // For now, assume all enumerated devices are connected
        return true
    }
    
    private func detectARKDaemon() -> Bool {
        // Check for ARK daemon process
        let task = Process()
        task.executableURL = URL(fileURLWithPath: "/bin/ps")
        task.arguments = ["-ax"]
        
        let pipe = Pipe()
        task.standardOutput = pipe
        
        do {
            try task.run()
            task.waitUntilExit()
            
            guard task.terminationStatus == 0 else {
                logger.error("ps command failed with status \(task.terminationStatus)")
                return false
            }
            
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            guard let output = String(data: data, encoding: .utf8) else {
                logger.error("Failed to decode ps command output")
                return false
            }
            
            return output.contains("ark") || output.contains("ARK")
        } catch {
            logger.error("Failed to check for ARK daemon: \(error)")
            return false
        }
    }
    
    private func detectSoundSource() -> Bool {
        // Check for SoundSource application
        let workspace = NSWorkspace.shared
        let runningApps = workspace.runningApplications
        
        return runningApps.contains { app in
            guard let bundleIdentifier = app.bundleIdentifier,
                  let localizedName = app.localizedName else {
                return false
            }
            return bundleIdentifier.contains("SoundSource") ||
                   localizedName.contains("SoundSource")
        }
    }
}

// MARK: - Notification Extensions

extension Notification.Name {
    static let audioDeviceListChanged = Notification.Name("audioDeviceListChanged")
}

// MARK: - Error Types
// Note: AudioEngineError is already defined in AudioEngineManager.swift
