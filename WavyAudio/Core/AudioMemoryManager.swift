//
// AudioMemoryManager.swift - Core Audio Memory Management & Leak Detection
// Specialized memory management for audio buffers and Core Audio operations
//

import Foundation
import CoreAudio
import os.log

class AudioMemoryManager {
    private let logger = Logger(subsystem: "com.wavyaudio.memory", category: "AudioMemory")
    private nonisolated(unsafe) static let shared = AudioMemoryManager()
    
    // Memory tracking
    private var allocatedBuffers: Set<UnsafeMutableRawPointer> = []
    private var allocationSizes: [UnsafeMutableRawPointer: Int] = [:]
    private let trackingQueue = DispatchQueue(label: "audio.memory.tracking", attributes: .concurrent)
    
    private init() {}
    
    // MARK: - Safe Audio Buffer Allocation
    
    static func allocateAudioBuffer<T>(type: T.Type, capacity: Int) -> UnsafeMutablePointer<T>? {
        let size = MemoryLayout<T>.stride * capacity
        
        guard let buffer = malloc(size) else {
            shared.logger.error("Failed to allocate audio buffer of size \(size) bytes")
            return nil
        }
        
        let typedBuffer = buffer.bindMemory(to: T.self, capacity: capacity)
        
        shared.trackingQueue.async(flags: .barrier) {
            shared.allocatedBuffers.insert(buffer)
            shared.allocationSizes[buffer] = size
        }
        
        shared.logger.debug("Allocated audio buffer: \(size) bytes at \(buffer.debugDescription)")
        return typedBuffer
    }
    
    static func deallocateAudioBuffer<T>(_ buffer: UnsafeMutablePointer<T>?) {
        guard let buffer = buffer else { return }
        
        let rawBuffer = UnsafeMutableRawPointer(buffer)
      i  
        shared.trackingQueue.async(flags: .barrier) { [weak shared] in
            guard let shared = shared else { return }
            if shared.allocatedBuffers.contains(rawBuffer) {
                let size = shared.allocationSizes.removeValue(forKey: rawBuffer) ?? 0
                shared.allocatedBuffers.remove(rawBuffer)
                shared.logger.debug("Deallocated audio buffer: \(size) bytes at \(rawBuffer.debugDescription)")
            } else {
                shared.logger.warning("Attempted to deallocate untracked buffer at \(rawBuffer.debugDescription)")
            }
        }
        
        free(rawBuffer)
    }
    
    // MARK: - Core Audio Property Memory Management
    
    static func safeGetProperty<T>(
        objectID: AudioObjectID,
        address: AudioObjectPropertyAddress,
        type: T.Type
    ) -> T? {
        var propertySize: UInt32 = 0
        var mutableAddress = address
        
        // Get property size
        let sizeResult = AudioObjectGetPropertyDataSize(objectID, &mutableAddress, 0, nil, &propertySize)
        guard sizeResult == noErr, propertySize > 0 else {
            shared.logger.error("Failed to get property size for selector \(address.mSelector)")
            return nil
        }
        
        // Validate size matches expected type
        let expectedSize = UInt32(MemoryLayout<T>.size)
        guard propertySize >= expectedSize else {
            shared.logger.error("Property size mismatch: expected \(expectedSize), got \(propertySize)")
            return nil
        }
        
        // Allocate and get property data
        let buffer = UnsafeMutablePointer<T>.allocate(capacity: 1)
        defer { buffer.deallocate() }
        
        var actualSize = propertySize
        var mutableAddress = address
        let dataResult = AudioObjectGetPropertyData(objectID, &mutableAddress, 0, nil, &actualSize, buffer)
        
        guard dataResult == noErr else {
            shared.logger.error("Failed to get property data: \(dataResult)")
            return nil
        }
        
        return buffer.pointee
    }
    
    static func safeGetPropertyArray<T>(
        objectID: AudioObjectID,
        address: AudioObjectPropertyAddress,
        type: T.Type
    ) -> [T]? {
        var propertySize: UInt32 = 0
        var mutableAddress = address
        
        // Get property size
        let sizeResult = AudioObjectGetPropertyDataSize(objectID, &mutableAddress, 0, nil, &propertySize)
        guard sizeResult == noErr, propertySize > 0 else {
            shared.logger.error("Failed to get property array size for selector \(address.mSelector)")
            return nil
        }
        
        let elementSize = MemoryLayout<T>.size
        let count = Int(propertySize) / elementSize
        
        guard count > 0 else {
            shared.logger.warning("Property array has zero elements")
            return []
        }
        
        // Allocate buffer for array
        let buffer = UnsafeMutablePointer<T>.allocate(capacity: count)
        defer { buffer.deallocate() }
        
        var actualSize = propertySize
        var mutableAddress = address
        let dataResult = AudioObjectGetPropertyData(objectID, &mutableAddress, 0, nil, &actualSize, buffer)
        
        guard dataResult == noErr else {
            shared.logger.error("Failed to get property array data: \(dataResult)")
            return nil
        }
        
        // Convert to Swift array
        var result: [T] = []
        for i in 0..<count {
            result.append(buffer[i])
        }
        
        return result
    }
    
    // MARK: - AudioBufferList Management
    
    static func createAudioBufferList(channels: Int, frameCount: Int) -> (UnsafeMutableAudioBufferListPointer, () -> Void) {
        let bufferListSize = AudioBufferList.sizeInBytes(maximumBuffers: channels)
        let bufferListPointer = UnsafeMutablePointer<AudioBufferList>.allocate(capacity: 1)
        let bufferList = UnsafeMutableAudioBufferListPointer(bufferListPointer)
        
        // Initialize buffer list
        bufferList.unsafeMutablePointer.pointee.mNumberBuffers = UInt32(channels)
        
        // Allocate audio data buffers
        let frameSize = frameCount * MemoryLayout<Float32>.size
        var allocatedPointers: [UnsafeMutableRawPointer] = []
        
        for i in 0..<channels {
            guard let audioData = malloc(frameSize) else {
                // Clean up any previously allocated buffers
                for ptr in allocatedPointers {
                    free(ptr)
                }
                bufferListPointer.deallocate()
                fatalError("Failed to allocate audio data buffer")
            }
            
            allocatedPointers.append(audioData)
            
            bufferList[i] = AudioBuffer(
                mNumberChannels: 1,
                mDataByteSize: UInt32(frameSize),
                mData: audioData
            )
        }
        
        // Return cleanup closure
        let cleanup = {
            for ptr in allocatedPointers {
                free(ptr)
            }
            bufferListPointer.deallocate()
        }
        
        return (bufferList, cleanup)
    }
    
    // MARK: - Memory Leak Detection
    
    static func detectMemoryLeaks() -> MemoryLeakReport {
        return shared.trackingQueue.sync {
            let totalAllocations = shared.allocatedBuffers.count
            let totalSize = shared.allocationSizes.values.reduce(0, +)
            
            shared.logger.info("Memory leak detection: \(totalAllocations) active allocations, \(totalSize) bytes")
            
            return MemoryLeakReport(
                activeAllocations: totalAllocations,
                totalAllocatedBytes: totalSize,
                allocatedBuffers: Array(shared.allocatedBuffers),
                timestamp: Date()
            )
        }
    }
    
    static func forceCleanup() {
        shared.trackingQueue.async(flags: .barrier) {
            shared.logger.warning("Force cleanup: deallocating \(shared.allocatedBuffers.count) tracked buffers")
            
            for buffer in shared.allocatedBuffers {
                free(buffer)
            }
            
            shared.allocatedBuffers.removeAll()
            shared.allocationSizes.removeAll()
        }
    }
    
    // MARK: - Performance Optimization
    
    static func optimizeForRealTimeAudio() {
        // Pre-allocate common buffer sizes
        let commonSizes = [64, 128, 256, 512, 1024, 2048]
        
        for size in commonSizes {
            if let buffer = allocateAudioBuffer(type: Float32.self, capacity: size) {
                // Immediately return to pool (would be managed by AudioBufferPool)
                deallocateAudioBuffer(buffer)
            }
        }
        
        shared.logger.info("Memory optimization for real-time audio completed")
    }
}

// MARK: - Memory Leak Report

struct MemoryLeakReport {
    let activeAllocations: Int
    let totalAllocatedBytes: Int
    let allocatedBuffers: [UnsafeMutableRawPointer]
    let timestamp: Date
    
    var hasLeaks: Bool {
        return activeAllocations > 0
    }
    
    var severity: Severity {
        if totalAllocatedBytes > 50 * 1024 * 1024 { // > 50MB
            return .critical
        } else if totalAllocatedBytes > 10 * 1024 * 1024 { // > 10MB
            return .warning
        } else if activeAllocations > 100 {
            return .warning
        } else {
            return .normal
        }
    }
    
    enum Severity {
        case normal, warning, critical
    }
}

// MARK: - Safe Wrapper Functions

extension AudioMemoryManager {
    
    /// Safe wrapper for AudioObjectGetPropertyData that handles memory management
    static func getAudioObjectProperty<T>(
        objectID: AudioObjectID,
        selector: AudioObjectPropertySelector,
        scope: AudioObjectPropertyScope = kAudioObjectPropertyScopeGlobal,
        element: AudioObjectPropertyElement = kAudioObjectPropertyElementMain,
        type: T.Type
    ) -> T? {
        var address = AudioObjectPropertyAddress(
            mSelector: selector,
            mScope: scope,
            mElement: element
        )
        
        return safeGetProperty(objectID: objectID, address: address, type: type)
    }
    
    /// Safe wrapper for getting array properties
    static func getAudioObjectPropertyArray<T>(
        objectID: AudioObjectID,
        selector: AudioObjectPropertySelector,
        scope: AudioObjectPropertyScope = kAudioObjectPropertyScopeGlobal,
        element: AudioObjectPropertyElement = kAudioObjectPropertyElementMain,
        type: T.Type
    ) -> [T]? {
        var address = AudioObjectPropertyAddress(
            mSelector: selector,
            mScope: scope,
            mElement: element
        )
        
        return safeGetPropertyArray(objectID: objectID, address: address, type: type)
    }
}
