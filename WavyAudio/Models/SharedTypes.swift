import Foundation
import CoreAudio

// MARK: - System Conflicts removed from here - use SystemConflict.swift for comprehensive definitions

// MARK: - Performance Optimizer

class PerformanceOptimizer {
    static func configureForAppleSilicon() {
        // Set process QoS
        pthread_set_qos_class_self_np(QOS_CLASS_USER_INTERACTIVE, 0)
        
        // Configure memory alignment for SIMD operations
        configureMemoryAlignment()
        
        // Setup vDSP optimizations
        configureVectorDSP()
        
        print("Apple Silicon optimization configured")
    }
    
    private static func configureMemoryAlignment() {
        // Ensure memory allocations are aligned for NEON SIMD operations
        // This is typically handled automatically on Apple Silicon
    }
    
    private static func configureVectorDSP() {
        // vDSP is automatically optimized for Apple Silicon
        // No explicit configuration needed
    }
    
    static func measurePerformance<T>(
        _ operation: () throws -> T
    ) rethrows -> (result: T, duration: TimeInterval) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try operation()
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        return (result, duration)
    }
}

// MARK: - Audio Processing Types

struct AudioBufferMetrics {
    let inputBufferSize: UInt32
    let outputBufferSize: UInt32
    let sampleRate: Float64
    let channelCount: UInt32
    let currentLoad: Float
}

// MARK: - Device Status Types

enum DeviceStatus {
    case active
    case inactive
    case unavailable
    case error(String)
}

// MARK: - Connection Types

enum ConnectionType {
    case direct
    case aggregated
    case virtual
    case networked
}

// MARK: - Quality of Service Types

enum AudioQOS {
    case realtime
    case interactive
    case utility
    case background
    
    var qosClass: qos_class_t {
        switch self {
        case .realtime:
            return QOS_CLASS_USER_INTERACTIVE
        case .interactive:
            return QOS_CLASS_USER_INTERACTIVE
        case .utility:
            return QOS_CLASS_UTILITY
        case .background:
            return QOS_CLASS_BACKGROUND
        }
    }
}