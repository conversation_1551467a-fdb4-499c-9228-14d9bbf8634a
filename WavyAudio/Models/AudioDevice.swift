//
// AudioDevice.swift - Audio Device Data Model
// WWDC Compliance Reference:
// - WWDC25-268: Swift 6 concurrency (Sendable conformance, thread safety)
// - WWDC21-10190: DriverKit Audio (Device property representation)
// - WWDC25-251: AVFoundation advances (Device metadata and capabilities)
// - WWDC19-608: Metal for Pro Apps (Metal-compatible data structures)
//
// WWDC Research Tool: python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-268
//

import Foundation
import CoreAudio
import SwiftUI

// MARK: - Audio Device Model

struct AudioDevice: Identifiable, Hashable, Codable, @unchecked Sendable {
    let id: UUID
    let deviceID: AudioDeviceID
    let name: String
    let manufacturer: String
    let inputChannels: Int
    let outputChannels: Int
    let sampleRate: Float64
    let isVirtual: Bool
    let isAggregate: Bool

    // UI-specific properties (mutable for SwiftUI)
    var position: CGPoint = CGPoint(x: 100, y: 100)
    var isSelected: Bool = false
    var connectionStrength: Double = 0.0

    // Connection status properties
    var isInputConnected: Bool = false
    var isOutputConnected: Bool = false

    // Device capabilities
    var canRecord: Bool { inputChannels > 0 }
    var canPlayback: Bool { outputChannels > 0 }
    var isBidirectional: Bool { canRecord && canPlayback }
    
    // Device type classification
    var deviceType: AudioDeviceType {
        if isAggregate { return .aggregate }
        if isVirtual { return .virtual }
        if isBidirectional { return .interface }
        if canRecord { return .input }
        if canPlayback { return .output }
        return .unknown
    }
    
    // Display properties
    var displayName: String {
        return name.isEmpty ? "Unknown Device" : name
    }
    
    var channelDescription: String {
        if isBidirectional {
            return "\(inputChannels) in, \(outputChannels) out"
        } else if canRecord {
            return "\(inputChannels) input channels"
        } else if canPlayback {
            return "\(outputChannels) output channels"
        } else {
            return "No channels"
        }
    }
    
    var sampleRateDescription: String {
        if sampleRate >= 1000 {
            return String(format: "%.1f kHz", sampleRate / 1000.0)
        } else {
            return String(format: "%.0f Hz", sampleRate)
        }
    }

    nonisolated enum CodingKeys: String, CodingKey {
        case id, deviceID, name, manufacturer, inputChannels, outputChannels, sampleRate, isVirtual, isAggregate, position, isSelected, connectionStrength, isInputConnected, isOutputConnected
    }

    nonisolated public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        deviceID = try container.decode(AudioDeviceID.self, forKey: .deviceID)
        name = try container.decode(String.self, forKey: .name)
        manufacturer = try container.decode(String.self, forKey: .manufacturer)
        inputChannels = try container.decode(Int.self, forKey: .inputChannels)
        outputChannels = try container.decode(Int.self, forKey: .outputChannels)
        sampleRate = try container.decode(Float64.self, forKey: .sampleRate)
        isVirtual = try container.decode(Bool.self, forKey: .isVirtual)
        isAggregate = try container.decode(Bool.self, forKey: .isAggregate)
        position = try container.decode(CGPoint.self, forKey: .position)
        isSelected = try container.decode(Bool.self, forKey: .isSelected)
        connectionStrength = try container.decode(Double.self, forKey: .connectionStrength)
        isInputConnected = try container.decodeIfPresent(Bool.self, forKey: .isInputConnected) ?? false
        isOutputConnected = try container.decodeIfPresent(Bool.self, forKey: .isOutputConnected) ?? false
    }

    nonisolated public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(deviceID, forKey: .deviceID)
        try container.encode(name, forKey: .name)
        try container.encode(manufacturer, forKey: .manufacturer)
        try container.encode(inputChannels, forKey: .inputChannels)
        try container.encode(outputChannels, forKey: .outputChannels)
        try container.encode(sampleRate, forKey: .sampleRate)
        try container.encode(isVirtual, forKey: .isVirtual)
        try container.encode(isAggregate, forKey: .isAggregate)
        try container.encode(position, forKey: .position)
        try container.encode(isSelected, forKey: .isSelected)
        try container.encode(connectionStrength, forKey: .connectionStrength)
        try container.encode(isInputConnected, forKey: .isInputConnected)
        try container.encode(isOutputConnected, forKey: .isOutputConnected)
    }

    // Custom initializer
    init(
        id: UUID = UUID(),
        deviceID: AudioDeviceID,
        name: String,
        manufacturer: String,
        inputChannels: Int,
        outputChannels: Int,
        sampleRate: Float64,
        isVirtual: Bool = false,
        isAggregate: Bool = false,
        position: CGPoint = CGPoint(x: 100, y: 100)
    ) {
        self.id = id
        self.deviceID = deviceID
        self.name = name
        self.manufacturer = manufacturer
        self.inputChannels = inputChannels
        self.outputChannels = outputChannels
        self.sampleRate = sampleRate
        self.isVirtual = isVirtual
        self.isAggregate = isAggregate
        self.position = position
        self.isSelected = false
        self.connectionStrength = 0.0
        self.isInputConnected = false
        self.isOutputConnected = false
    }
}

// MARK: - Audio Device Type

enum AudioDeviceType: String, CaseIterable, Identifiable, Codable {
    case input = "Input"
    case output = "Output"
    case interface = "Interface"
    case virtual = "Virtual"
    case aggregate = "Aggregate"
    case unknown = "Unknown"
    
    var id: String { self.rawValue }
    
    var color: Color {
        switch self {
        case .input: return .blue
        case .output: return .purple
        case .interface: return .green
        case .virtual: return .orange
        case .aggregate: return .red
        case .unknown: return .gray
        }
    }
    
    var icon: String {
        switch self {
        case .input: return "mic.fill"
        case .output: return "speaker.wave.3.fill"
        case .interface: return "cable.connector" // Physical interface hardware
        case .virtual: return "waveform.circle.fill" // Virtual/software device
        case .aggregate: return "square.stack.3d.up.fill" // Multiple devices combined
        case .unknown: return "questionmark.circle.fill"
        }
    }
    
    // Secondary icon to show device distinction
    var deviceStatusIcon: String? {
        switch self {
        case .virtual:
            return "cloud.fill" // Cloud icon for virtual/software devices
        case .aggregate:
            return "link" // Link icon for aggregated devices
        case .interface:
            return "cable.connector.horizontal" // Hardware connector
        default:
            return nil
        }
    }
    
    // Description for accessibility
    var accessibilityDescription: String {
        switch self {
        case .input: return "Audio input device"
        case .output: return "Audio output device"
        case .interface: return "Audio interface hardware"
        case .virtual: return "Virtual audio device"
        case .aggregate: return "Aggregate audio device"
        case .unknown: return "Unknown audio device"
        }
    }
}

// MARK: - Audio Device Extensions

extension AudioDevice {
    // Check compatibility with another device
    func isCompatible(with other: AudioDevice) -> Bool {
        // Basic compatibility check based on sample rate
        return abs(self.sampleRate - other.sampleRate) < 1000.0
    }
    
    // Calculate optimal connection position
    func connectionPoint(to other: AudioDevice) -> CGPoint {
        let midX = (self.position.x + other.position.x) / 2
        let midY = (self.position.y + other.position.y) / 2
        return CGPoint(x: midX, y: midY)
    }
    
    #if DEBUG
    // Mock devices for testing and previews
    static let mockDevices: [AudioDevice] = [
        AudioDevice(
            deviceID: 1,
            name: "Built-in Microphone",
            manufacturer: "Apple",
            inputChannels: 2,
            outputChannels: 0,
            sampleRate: 44100.0,
            position: CGPoint(x: 50, y: 50)
        ),
        AudioDevice(
            deviceID: 2,
            name: "Built-in Output",
            manufacturer: "Apple",
            inputChannels: 0,
            outputChannels: 2,
            sampleRate: 44100.0,
            position: CGPoint(x: 200, y: 50)
        ),
        AudioDevice(
            deviceID: 3,
            name: "AudioBox USB 96",
            manufacturer: "PreSonus",
            inputChannels: 2,
            outputChannels: 2,
            sampleRate: 48000.0,
            position: CGPoint(x: 50, y: 150)
        ),
        AudioDevice(
            deviceID: 4,
            name: "Aggregate Device",
            manufacturer: "Core Audio",
            inputChannels: 4,
            outputChannels: 4,
            sampleRate: 44100.0,
            isAggregate: true,
            position: CGPoint(x: 200, y: 150)
        ),
        AudioDevice(
            deviceID: 5,
            name: "BlackHole 16ch",
            manufacturer: "ExistentialAudio Inc.",
            inputChannels: 16,
            outputChannels: 16,
            sampleRate: 44100.0,
            isVirtual: true,
            position: CGPoint(x: 125, y: 250)
        )
    ]
    #endif
}


