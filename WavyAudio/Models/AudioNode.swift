import Foundation
import CoreAudio
import SwiftUI

// MARK: - Node Type

enum NodeType: String, CaseIterable, Identifiable, Codable, Equatable {
    case inputDevice = "Input Device"
    case outputDevice = "Output Device"
    case processor = "Processor"
    case mixer = "Mixer"
    
    var id: String { self.rawValue }
    
    var color: Color {
        switch self {
        case .inputDevice: return .blue
        case .outputDevice: return .purple
        case .processor: return .orange
        case .mixer: return .teal
        }
    }
    
    var size: CGSize {
        switch self {
        case .inputDevice, .outputDevice: return CGSize(width: 160, height: 60)
        case .processor, .mixer: return CGSize(width: 120, height: 80)
        }
    }
}

// MARK: - Audio Node

struct AudioNode: Identifiable, @preconcurrency Codable, Equatable {
    let id: UUID
    let deviceID: AudioDeviceID // Corresponds to AudioDevice.deviceID
    var deviceName: String
    var nodeType: NodeType
    var position: CGPoint
    var isSelected: Bool
    var inputChannels: Int
    var outputChannels: Int

    nonisolated enum CodingKeys: String, CodingKey {
        case id, deviceID, deviceName, nodeType, position, isSelected, inputChannels, outputChannels
    }

    nonisolated public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        deviceID = try container.decode(AudioDeviceID.self, forKey: .deviceID)
        deviceName = try container.decode(String.self, forKey: .deviceName)
        nodeType = try container.decode(NodeType.self, forKey: .nodeType)
        position = try container.decode(CGPoint.self, forKey: .position)
        isSelected = try container.decode(Bool.self, forKey: .isSelected)
        inputChannels = try container.decode(Int.self, forKey: .inputChannels)
        outputChannels = try container.decode(Int.self, forKey: .outputChannels)
    }

    nonisolated public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(deviceID, forKey: .deviceID)
        try container.encode(deviceName, forKey: .deviceName)
        try container.encode(nodeType, forKey: .nodeType)
        try container.encode(position, forKey: .position)
        try container.encode(isSelected, forKey: .isSelected)
        try container.encode(inputChannels, forKey: .inputChannels)
        try container.encode(outputChannels, forKey: .outputChannels)
    }
    
    init(from device: AudioDevice, position: CGPoint = .zero, isSelected: Bool = false) {
        self.id = UUID()
        self.deviceID = device.deviceID
        self.deviceName = device.name
        self.position = position
        self.isSelected = isSelected
        self.inputChannels = device.inputChannels
        self.outputChannels = device.outputChannels
        
        // Determine node type based on device properties
        if device.inputChannels > 0 && device.outputChannels == 0 {
            self.nodeType = .inputDevice
        } else if device.inputChannels == 0 && device.outputChannels > 0 {
            self.nodeType = .outputDevice
        } else {
            // For devices with both input/output or virtual/aggregate, default to processor or mixer
            self.nodeType = .processor // Could be refined based on more logic
        }
    }
    
    init(
        id: UUID = UUID(),
        deviceID: AudioDeviceID,
        deviceName: String,
        nodeType: NodeType,
        position: CGPoint,
        isSelected: Bool = false,
        inputChannels: Int,
        outputChannels: Int
    ) {
        self.id = id
        self.deviceID = deviceID
        self.deviceName = deviceName
        self.nodeType = nodeType
        self.position = position
        self.isSelected = isSelected
        self.inputChannels = inputChannels
        self.outputChannels = outputChannels
    }
}

// MARK: - Audio Node Extensions

extension AudioNode {
    var displayName: String {
        return deviceName.isEmpty ? "Unknown Node" : deviceName
    }
    
    var channelDescription: String {
        switch nodeType {
        case .inputDevice:
            return "\(inputChannels) in"
        case .outputDevice:
            return "\(outputChannels) out"
        case .processor, .mixer:
            return "\(inputChannels) in, \(outputChannels) out"
        }
    }
    
    var canConnect: Bool {
        return inputChannels > 0 || outputChannels > 0
    }
    
    var isSource: Bool {
        return outputChannels > 0
    }
    
    var isDestination: Bool {
        return inputChannels > 0
    }
    
    func canConnectTo(_ other: AudioNode) -> Bool {
        return self.isSource && other.isDestination
    }
    
    func connectionPoint(for direction: ConnectionDirection) -> CGPoint {
        let nodeSize = nodeType.size
        switch direction {
        case .input:
            return CGPoint(x: position.x, y: position.y + nodeSize.height / 2)
        case .output:
            return CGPoint(x: position.x + nodeSize.width, y: position.y + nodeSize.height / 2)
        }
    }
}

// MARK: - Connection Direction

enum ConnectionDirection {
    case input
    case output
}




