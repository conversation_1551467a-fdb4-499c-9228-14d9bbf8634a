import Foundation
import SwiftUI
import CoreAudio

// MARK: - Audio Connection Model

struct AudioConnection: Identifiable, Hashable, Codable {
    let id: UUID
    let sourceDeviceID: AudioDeviceID
    let destinationDeviceID: AudioDeviceID
    
    // Connection properties
    var sourceChannels: [Int] = []
    var destinationChannels: [Int] = []
    var gain: Float = 1.0
    var isActive: Bool = true
    var connectionType: AudioConnectionType = .direct
    
    // Signal properties
    var signalStrength: Double = 0.0
    var latency: TimeInterval = 0.0
    var sampleRate: Float64 = 48000.0
    
    // UI properties
    var startPoint: CGPoint = .zero
    var endPoint: CGPoint = .zero
    var controlPoints: [CGPoint] = []
    var isSelected: Bool = false
    
    // Animation properties
    var animationPhase: Double = 0.0
    var pulseIntensity: Double = 0.0
    
    init(
        id: UUID = UUID(),
        sourceDeviceID: AudioDeviceID,
        destinationDeviceID: AudioDeviceID,
        sourceChannels: [Int] = [],
        destinationChannels: [Int] = [],
        gain: Float = 1.0,
        connectionType: AudioConnectionType = .direct
    ) {
        self.id = id
        self.sourceDeviceID = sourceDeviceID
        self.destinationDeviceID = destinationDeviceID
        self.sourceChannels = sourceChannels
        self.destinationChannels = destinationChannels
        self.gain = gain
        self.connectionType = connectionType
    }
    
    // Convenience initializer from devices
    init(from source: AudioDevice, to destination: AudioDevice) {
        self.init(
            sourceDeviceID: source.deviceID,
            destinationDeviceID: destination.deviceID,
            sourceChannels: Array(0..<source.outputChannels),
            destinationChannels: Array(0..<destination.inputChannels)
        )
        
        // Set positions from device positions
        self.startPoint = source.position
        self.endPoint = destination.position
        self.sampleRate = min(source.sampleRate, destination.sampleRate)
    }
}

// MARK: - Audio Connection Type

enum AudioConnectionType: String, CaseIterable, Codable {
    case direct = "Direct"
    case routed = "Routed"
    case mixed = "Mixed"
    case processed = "Processed"
    case aggregate = "Aggregate"
    
    var color: Color {
        switch self {
        case .direct: return .blue
        case .routed: return .green
        case .mixed: return .purple
        case .processed: return .orange
        case .aggregate: return .red
        }
    }
    
    var lineWidth: CGFloat {
        switch self {
        case .direct: return 2.0
        case .routed: return 3.0
        case .mixed: return 4.0
        case .processed: return 3.5
        case .aggregate: return 5.0
        }
    }
    
    var description: String {
        switch self {
        case .direct: return "Direct audio connection"
        case .routed: return "Routed through system"
        case .mixed: return "Mixed signal connection"
        case .processed: return "Processed audio connection"
        case .aggregate: return "Aggregate device connection"
        }
    }
    
    var pattern: [CGFloat] {
        switch self {
        case .direct: return []
        case .routed: return [5, 5]
        case .mixed: return [10, 5, 2, 5]
        case .processed: return [15, 5]
        case .aggregate: return [20, 10]
        }
    }
}

// MARK: - Audio Connection Status

enum AudioConnectionStatus: String, CaseIterable {
    case active = "Active"
    case inactive = "Inactive"
    case error = "Error"
    case connecting = "Connecting"
    case buffering = "Buffering"
    case dropping = "Dropping"
    
    var color: Color {
        switch self {
        case .active: return .green
        case .inactive: return .gray
        case .error: return .red
        case .connecting: return .orange
        case .buffering: return .yellow
        case .dropping: return .red.opacity(0.7)
        }
    }
    
    var icon: String {
        switch self {
        case .active: return "checkmark.circle.fill"
        case .inactive: return "pause.circle"
        case .error: return "exclamationmark.triangle.fill"
        case .connecting: return "arrow.clockwise"
        case .buffering: return "hourglass"
        case .dropping: return "exclamationmark.circle"
        }
    }
}

// ChannelMapping is now defined in ChannelMapping.swift

// MARK: - Audio Signal Flow

struct AudioSignalFlow {
    let connectionID: UUID
    var bufferSize: UInt32
    var currentLatency: TimeInterval
    var droppedSamples: Int
    var signalLevel: Float
    var peakLevel: Float
    var rmsLevel: Float
    
    init(connectionID: UUID, bufferSize: UInt32 = 256) {
        self.connectionID = connectionID
        self.bufferSize = bufferSize
        self.currentLatency = 0.0
        self.droppedSamples = 0
        self.signalLevel = 0.0
        self.peakLevel = 0.0
        self.rmsLevel = 0.0
    }
    
    mutating func updateSignalLevels(peak: Float, rms: Float) {
        self.peakLevel = peak
        self.rmsLevel = rms
        self.signalLevel = max(peak, rms)
    }
    
    mutating func recordDroppedSamples(_ count: Int) {
        droppedSamples += count
    }
    
    mutating func updateLatency(_ latency: TimeInterval) {
        currentLatency = latency
    }
}

// MARK: - Audio Connection Extensions

extension AudioConnection {
    
    // Calculate connection path for UI rendering
    func calculatePath() -> Path {
        var path = Path()
        path.move(to: startPoint)
        
        if controlPoints.isEmpty {
            // Simple straight line
            path.addLine(to: endPoint)
        } else {
            // Curved connection with control points
            for (index, controlPoint) in controlPoints.enumerated() {
                if index == controlPoints.count - 1 {
                    path.addQuadCurve(to: endPoint, control: controlPoint)
                } else {
                    path.addQuadCurve(to: controlPoints[index + 1], control: controlPoint)
                }
            }
        }
        
        return path
    }
    
    // Generate control points for smooth curves
    mutating func generateControlPoints() {
        let midPoint = CGPoint(
            x: (startPoint.x + endPoint.x) / 2,
            y: (startPoint.y + endPoint.y) / 2
        )
        
        let distance = sqrt(pow(endPoint.x - startPoint.x, 2) + pow(endPoint.y - startPoint.y, 2))
        let offset = distance * 0.2
        
        // Calculate perpendicular offset for curve
        let angle = atan2(endPoint.y - startPoint.y, endPoint.x - startPoint.x)
        let perpendicularAngle = angle + .pi / 2
        
        let controlPoint = CGPoint(
            x: midPoint.x + cos(perpendicularAngle) * offset,
            y: midPoint.y + sin(perpendicularAngle) * offset
        )
        
        controlPoints = [controlPoint]
    }
    
    // Update animation phase for flowing signals
    mutating func updateAnimation(deltaTime: TimeInterval) {
        animationPhase += deltaTime * 2.0 // 2 cycles per second
        if animationPhase > 2.0 * .pi {
            animationPhase -= 2.0 * .pi
        }
        
        // Update pulse intensity based on signal strength
        pulseIntensity = 0.5 + 0.5 * sin(animationPhase) * signalStrength
    }
    
    // SIMD-optimized batch animation update
    static func updateAnimationsBatch(_ connections: inout [AudioConnection], deltaTime: TimeInterval) {
        // Use SIMD for vectorized phase calculations
        for i in stride(from: 0, to: connections.count, by: 4) {
            let endIndex = min(i + 4, connections.count)
            let batchSize = endIndex - i
            
            for j in 0..<batchSize {
                let index = i + j
                connections[index].animationPhase += deltaTime * 2.0
                if connections[index].animationPhase > 2.0 * .pi {
                    connections[index].animationPhase -= 2.0 * .pi
                }
                connections[index].pulseIntensity = 0.5 + 0.5 * sin(connections[index].animationPhase) * connections[index].signalStrength
            }
        }
    }
    
    // Check if connection is valid
    var isValid: Bool {
        return sourceDeviceID != destinationDeviceID &&
               !sourceChannels.isEmpty &&
               !destinationChannels.isEmpty
    }
    
    // Calculate total latency including processing
    var totalLatency: TimeInterval {
        let bufferLatency = Double(256) / sampleRate  // Assuming 256 sample buffer
        let processingLatency = latency
        return bufferLatency + processingLatency
    }
    
    // Note: Channel mapping generation moved to ChannelMapping extension
    // to avoid circular dependency between AudioConnection and ChannelMapping
    
    // Calculate connection quality score
    var qualityScore: Double {
        var score = 1.0
        
        // Penalize high latency
        if totalLatency > 0.010 {  // 10ms threshold
            score *= 0.8
        }
        
        // Penalize low signal strength
        if signalStrength < 0.5 {
            score *= signalStrength + 0.5
        }
        
        // Penalize if inactive
        if !isActive {
            score *= 0.1
        }
        
        return max(0.0, min(1.0, score))
    }
}

// MARK: - Audio Connection Collection Extensions

extension Array where Element == AudioConnection {
    
    // Find connections for a specific device
    func connections(for deviceID: AudioDeviceID) -> [AudioConnection] {
        return filter { $0.sourceDeviceID == deviceID || $0.destinationDeviceID == deviceID }
    }
    
    func sourceConnections(for deviceID: AudioDeviceID) -> [AudioConnection] {
        return filter { $0.sourceDeviceID == deviceID }
    }
    
    func destinationConnections(for deviceID: AudioDeviceID) -> [AudioConnection] {
        return filter { $0.destinationDeviceID == deviceID }
    }
    
    // Find connection between two devices
    func connection(from sourceID: AudioDeviceID, to destinationID: AudioDeviceID) -> AudioConnection? {
        return first { $0.sourceDeviceID == sourceID && $0.destinationDeviceID == destinationID }
    }
    
    // Filter by status
    func activeConnections() -> [AudioConnection] {
        return filter { $0.isActive }
    }
    
    func inactiveConnections() -> [AudioConnection] {
        return filter { !$0.isActive }
    }
    
    // Filter by type
    func connections(ofType type: AudioConnectionType) -> [AudioConnection] {
        return filter { $0.connectionType == type }
    }
    
    // Calculate total signal flow
    var totalSignalStrength: Double {
        return activeConnections().reduce(0) { $0 + $1.signalStrength }
    }
    
    // Find potential routing conflicts
    func findRoutingConflicts() -> [(AudioConnection, AudioConnection)] {
        var conflicts: [(AudioConnection, AudioConnection)] = []
        
        for i in 0..<count {
            for j in (i+1)..<count {
                let connection1 = self[i]
                let connection2 = self[j]
                
                // Check for device conflicts
                if connection1.destinationDeviceID == connection2.destinationDeviceID {
                    // Same destination device - potential mixing conflict
                    conflicts.append((connection1, connection2))
                }
            }
        }
        
        return conflicts
    }
}

