<?xml version="1.0" encoding="UTF-8"?>
<plist version="1.0">
<dict>
    <!-- Core Audio Direct Access -->
    <key>com.apple.security.device.audio-input</key>
    <true/>
    <key>com.apple.security.device.microphone</key>
    <true/>

    <!-- Metal Performance Shaders -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <false/>

    <!-- Hardware Abstraction -->
    <key>com.apple.security.temporary-exception.audio-unit-host</key>
    <true/>

    <!-- Modern App Capabilities -->
    <key>com.apple.security.app-sandbox</key>
    <true/>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
</dict>
</plist>
