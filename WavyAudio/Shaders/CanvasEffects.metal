//
// CanvasEffects.metal - Canvas Background and UI Effect Shaders
// Part of WavyAudio - Professional Audio Routing Application
//

#include <metal_stdlib>
using namespace metal;

// MARK: - Shared Structures

struct VertexIn {
    float2 position [[attribute(0)]];
    float2 texCoord [[attribute(1)]];
};

struct VertexOut {
    float4 position [[position]];
    float2 texCoord;
    float2 worldPos;
};

struct CanvasUniforms {
    float4x4 projectionMatrix;
    float4x4 viewMatrix;
    float time;
    float zoom;
    float2 offset;
    float gridSpacing;
    float nodeCount;
};

// MARK: - Grid Background

vertex VertexOut grid_vertex(VertexIn in [[stage_in]],
                            constant CanvasUniforms& uniforms [[buffer(0)]]) {
    VertexOut out;
    out.position = uniforms.projectionMatrix * float4(in.position, 0.0, 1.0);
    out.texCoord = in.texCoord;
    
    // Calculate world position for grid alignment
    float4 worldPos = uniforms.viewMatrix * float4(in.position, 0.0, 1.0);
    out.worldPos = worldPos.xy;
    
    return out;
}

fragment float4 grid_fragment(VertexOut in [[stage_in]],
                             constant CanvasUniforms& uniforms [[buffer(0)]]) {
    // Dynamic grid based on zoom level
    float gridSize = uniforms.gridSpacing * uniforms.zoom;
    
    // Calculate grid lines
    float2 gridPos = (in.worldPos + uniforms.offset) / gridSize;
    float2 grid = abs(fract(gridPos - 0.5) - 0.5) / fwidth(gridPos);
    float gridLine = 1.0 - min(min(grid.x, grid.y), 1.0);
    
    // Major grid lines every 5 units
    float2 majorGridPos = gridPos / 5.0;
    float2 majorGrid = abs(fract(majorGridPos - 0.5) - 0.5) / fwidth(majorGridPos);
    float majorGridLine = 1.0 - min(min(majorGrid.x, majorGrid.y), 1.0);
    
    // Grid colors
    float3 minorColor = float3(0.15, 0.15, 0.2);
    float3 majorColor = float3(0.25, 0.25, 0.3);
    
    // Combine grid lines
    float3 color = mix(float3(0.05, 0.05, 0.08), minorColor, gridLine * 0.3);
    color = mix(color, majorColor, majorGridLine * 0.5);
    
    // Fade grid based on zoom
    float fadeAlpha = smoothstep(0.1, 0.5, uniforms.zoom);
    
    return float4(color, fadeAlpha);
}

// MARK: - Canvas Background Noise

fragment float4 noise_background_fragment(VertexOut in [[stage_in]],
                                         constant CanvasUniforms& uniforms [[buffer(0)]]) {
    // Procedural noise for subtle texture
    float2 uv = in.texCoord * 100.0 + uniforms.time * 0.1;
    
    // Simple noise function
    float noise = fract(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
    noise = smoothstep(0.4, 0.6, noise);
    
    // Very subtle noise overlay
    float3 baseColor = float3(0.02, 0.02, 0.025);
    float3 color = baseColor + noise * 0.015;
    
    return float4(color, 1.0);
}

// MARK: - Node Shadow

vertex VertexOut shadow_vertex(VertexIn in [[stage_in]],
                              constant CanvasUniforms& uniforms [[buffer(0)]]) {
    VertexOut out;
    
    // Offset shadow slightly
    float2 shadowOffset = float2(0.02, -0.02) / uniforms.zoom;
    float2 position = in.position + shadowOffset;
    
    out.position = uniforms.projectionMatrix * float4(position, 0.0, 1.0);
    out.texCoord = in.texCoord;
    out.worldPos = position;
    
    return out;
}

fragment float4 shadow_fragment(VertexOut in [[stage_in]],
                               constant CanvasUniforms& uniforms [[buffer(0)]]) {
    float2 center = float2(0.5, 0.5);
    float distance = length(in.texCoord - center);
    
    // Soft shadow falloff
    float shadow = 1.0 - smoothstep(0.0, 0.5, distance);
    shadow = pow(shadow, 2.0); // Make shadow softer
    
    return float4(0.0, 0.0, 0.0, shadow * 0.3);
}

// MARK: - Selection Highlight

fragment float4 selection_fragment(VertexOut in [[stage_in]],
                                  constant CanvasUniforms& uniforms [[buffer(0)]]) {
    float2 center = float2(0.5, 0.5);
    float distance = length(in.texCoord - center);
    
    // Animated selection ring
    float ring = abs(distance - 0.4);
    float pulse = sin(uniforms.time * 4.0) * 0.5 + 0.5;
    float ringAlpha = 1.0 - smoothstep(0.0, 0.05, ring);
    
    // Selection color with animation
    float3 color = float3(0.3 + pulse * 0.7, 0.8, 1.0);
    
    return float4(color, ringAlpha * 0.8);
}

// MARK: - Bezier Connection Curves

struct BezierUniforms {
    float4x4 projectionMatrix;
    float2 startPoint;
    float2 endPoint;
    float2 controlPoint1;
    float2 controlPoint2;
    float time;
    float thickness;
    float3 color;
};

vertex VertexOut bezier_vertex(VertexIn in [[stage_in]],
                              constant BezierUniforms& uniforms [[buffer(0)]]) {
    VertexOut out;
    
    // Generate bezier curve points
    float t = in.texCoord.x;
    float2 bezierPoint = 
        pow(1.0 - t, 3.0) * uniforms.startPoint +
        3.0 * pow(1.0 - t, 2.0) * t * uniforms.controlPoint1 +
        3.0 * (1.0 - t) * pow(t, 2.0) * uniforms.controlPoint2 +
        pow(t, 3.0) * uniforms.endPoint;
    
    // Perpendicular offset for line thickness
    float2 tangent = normalize(
        3.0 * pow(1.0 - t, 2.0) * (uniforms.controlPoint1 - uniforms.startPoint) +
        6.0 * (1.0 - t) * t * (uniforms.controlPoint2 - uniforms.controlPoint1) +
        3.0 * pow(t, 2.0) * (uniforms.endPoint - uniforms.controlPoint2)
    );
    float2 normal = float2(-tangent.y, tangent.x);
    
    float offset = (in.texCoord.y - 0.5) * uniforms.thickness;
    float2 position = bezierPoint + normal * offset;
    
    out.position = uniforms.projectionMatrix * float4(position, 0.0, 1.0);
    out.texCoord = in.texCoord;
    out.worldPos = position;
    
    return out;
}

fragment float4 bezier_fragment(VertexOut in [[stage_in]],
                               constant BezierUniforms& uniforms [[buffer(0)]]) {
    // Distance from curve center for anti-aliasing
    float distanceFromCenter = abs(in.texCoord.y - 0.5);
    float alpha = 1.0 - smoothstep(0.4, 0.5, distanceFromCenter);
    
    // Flowing animation along the curve
    float flow = sin(in.texCoord.x * 8.0 - uniforms.time * 3.0) * 0.3 + 0.7;
    
    // Color with flow effect
    float3 color = uniforms.color * flow;
    
    return float4(color, alpha * 0.9);
}

// MARK: - Particle Effects for Active Connections

struct ParticleUniforms {
    float4x4 projectionMatrix;
    float time;
    float2 startPoint;
    float2 endPoint;
    float particleCount;
    float speed;
};

vertex VertexOut particle_vertex(uint vertexID [[vertex_id]],
                                constant ParticleUniforms& uniforms [[buffer(0)]]) {
    VertexOut out;
    
    // Generate particle along connection line
    float particleIndex = float(vertexID % int(uniforms.particleCount));
    float t = fract((particleIndex / uniforms.particleCount) + uniforms.time * uniforms.speed);
    
    float2 position = mix(uniforms.startPoint, uniforms.endPoint, t);
    
    out.position = uniforms.projectionMatrix * float4(position, 0.0, 1.0);
    out.texCoord = float2(t, 0.5);
    out.worldPos = position;
    
    return out;
}

fragment float4 particle_fragment(VertexOut in [[stage_in]],
                                 constant ParticleUniforms& uniforms [[buffer(0)]]) {
    // Particle fade based on position along line
    float fade = sin(in.texCoord.x * 3.14159) * 0.8 + 0.2;
    
    // Particle color
    float3 color = float3(0.4, 0.9, 1.0);
    
    return float4(color, fade * 0.6);
}