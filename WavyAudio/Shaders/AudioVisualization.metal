//
// AudioVisualization.metal - GPU-Accelerated Audio Visualization Shaders
// Part of WavyAudio - Professional Audio Routing Application
//

#include <metal_stdlib>
using namespace metal;

// MARK: - Vertex Input/Output Structures

struct VertexIn {
    float2 position [[attribute(0)]];
    float2 texCoord [[attribute(1)]];
};

struct VertexOut {
    float4 position [[position]];
    float2 texCoord;
    float time;
};

struct Uniforms {
    float4x4 projectionMatrix;
    float time;
    float amplitude;
    float frequency;
    float sampleRate;
};

// MARK: - Waveform Visualization

vertex VertexOut waveform_vertex(VertexIn in [[stage_in]],
                                constant Uniforms& uniforms [[buffer(0)]]) {
    VertexOut out;
    out.position = uniforms.projectionMatrix * float4(in.position, 0.0, 1.0);
    out.texCoord = in.texCoord;
    out.time = uniforms.time;
    return out;
}

fragment float4 waveform_fragment(VertexOut in [[stage_in]],
                                 texture2d<float> audioTexture [[texture(0)]],
                                 constant Uniforms& uniforms [[buffer(0)]]) {
    constexpr sampler textureSampler(mag_filter::linear, min_filter::linear);
    
    // Sample audio data from texture
    float audioSample = audioTexture.sample(textureSampler, float2(in.texCoord.x, 0.5)).r;
    
    // Create waveform visualization
    float waveform = sin(in.texCoord.x * uniforms.frequency + uniforms.time * 2.0) * uniforms.amplitude;
    float distance = abs(in.texCoord.y - 0.5 - waveform * audioSample);
    
    // Anti-aliased line rendering
    float lineWidth = 0.01;
    float alpha = 1.0 - smoothstep(0.0, lineWidth, distance);
    
    // Color based on frequency content
    float3 color = float3(0.2 + audioSample * 0.8, 0.6, 1.0 - audioSample * 0.3);
    
    return float4(color * alpha, alpha);
}

// MARK: - Spectrum Analyzer

fragment float4 spectrum_fragment(VertexOut in [[stage_in]],
                                 texture2d<float> fftTexture [[texture(0)]],
                                 constant Uniforms& uniforms [[buffer(0)]]) {
    constexpr sampler textureSampler(mag_filter::linear, min_filter::linear);
    
    // Sample FFT magnitude data
    float magnitude = fftTexture.sample(textureSampler, float2(in.texCoord.x, 0.5)).r;
    
    // Logarithmic frequency scale for bar height calculation
    float logFreq = log2(in.texCoord.x * uniforms.sampleRate * 0.5) / log2(uniforms.sampleRate * 0.5);

    // Bar height based on magnitude with logarithmic scaling
    float barHeight = magnitude * 0.8 * (1.0 + logFreq * 0.2);
    float alpha = step(in.texCoord.y, barHeight);
    
    // Color gradient from bottom (red) to top (blue)
    float3 color = mix(float3(1.0, 0.2, 0.2), float3(0.2, 0.2, 1.0), in.texCoord.y);
    
    // Add glow effect
    float glow = exp(-pow(abs(in.texCoord.y - barHeight), 2.0) * 20.0) * 0.3;
    color += glow * float3(1.0, 1.0, 0.8);
    
    return float4(color * alpha, alpha);
}

// MARK: - VU Meter

fragment float4 vu_meter_fragment(VertexOut in [[stage_in]],
                                 constant Uniforms& uniforms [[buffer(0)]]) {
    float level = uniforms.amplitude; // Current audio level (0.0 to 1.0)
    
    // VU meter segments
    float segmentHeight = 1.0 / 12.0; // 12 segments
    int segment = int(in.texCoord.y / segmentHeight);
    float segmentLevel = float(segment) / 12.0;
    
    // Color coding: green (low), yellow (mid), red (high)
    float3 color;
    if (segmentLevel < 0.6) {
        color = float3(0.2, 1.0, 0.2); // Green
    } else if (segmentLevel < 0.85) {
        color = float3(1.0, 1.0, 0.2); // Yellow
    } else {
        color = float3(1.0, 0.2, 0.2); // Red
    }
    
    // Light up segments based on audio level
    float alpha = step(segmentLevel, level) * 0.9;
    
    // Add peak hold indicator
    float peakDistance = abs(in.texCoord.y - level);
    if (peakDistance < 0.02) {
        alpha = max(alpha, 0.8);
        color = float3(1.0, 1.0, 1.0); // White peak indicator
    }
    
    return float4(color, alpha);
}

// MARK: - Audio Node Glow Effect

fragment float4 node_glow_fragment(VertexOut in [[stage_in]],
                                  constant Uniforms& uniforms [[buffer(0)]]) {
    float2 center = float2(0.5, 0.5);
    float distance = length(in.texCoord - center);
    
    // Pulsing glow based on audio activity
    float pulse = sin(uniforms.time * 3.0) * 0.5 + 0.5;
    float intensity = uniforms.amplitude * pulse;
    
    // Radial glow falloff
    float glow = exp(-distance * distance * 8.0) * intensity;
    
    // Color based on node type (could be passed as additional uniform)
    float3 color = float3(0.2, 0.8, 1.0); // Blue for default
    
    return float4(color * glow, glow * 0.7);
}

// MARK: - Connection Line Animation

vertex VertexOut connection_vertex(VertexIn in [[stage_in]],
                                  constant Uniforms& uniforms [[buffer(0)]]) {
    VertexOut out;
    
    // Animate connection line with flowing effect
    float2 position = in.position;
    float flowOffset = sin(uniforms.time * 2.0 + in.texCoord.x * 10.0) * 0.01;
    position.y += flowOffset;
    
    out.position = uniforms.projectionMatrix * float4(position, 0.0, 1.0);
    out.texCoord = in.texCoord;
    out.time = uniforms.time;
    return out;
}

fragment float4 connection_fragment(VertexOut in [[stage_in]],
                                   constant Uniforms& uniforms [[buffer(0)]]) {
    // Flowing energy effect along connection lines
    float flow = sin(in.texCoord.x * 8.0 - uniforms.time * 4.0) * 0.5 + 0.5;
    float energy = uniforms.amplitude * flow;
    
    // Line thickness and anti-aliasing
    float lineDistance = abs(in.texCoord.y - 0.5);
    float lineWidth = 0.1;
    float alpha = 1.0 - smoothstep(lineWidth * 0.5, lineWidth, lineDistance);
    
    // Color pulse with audio activity
    float3 color = float3(0.3 + energy * 0.7, 0.8, 1.0);
    
    return float4(color * alpha, alpha * 0.8);
}