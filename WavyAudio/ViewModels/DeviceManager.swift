import Foundation
import Combine
import Observation
import os

// MARK: - Mock Device Factory for Development
#if DEBUG
class MockDeviceFactory {
    func mockDevices() -> [AudioDevice] {
        return AudioDevice.mockDevices
    }
}
#endif

@MainActor
@Observable
class DeviceManager {

    // Sample data for demonstration purposes
    #if DEBUG
    var devices: [AudioDevice] = MockDeviceFactory().mockDevices()
    #else
    var devices: [AudioDevice] = []
    #endif
    var systemConflicts: [SystemConflict] = []
    func createDeviceStatusIndicator(device: AudioDevice) -> String {
        // Placeholder for actual system-health checks or hardware status monitoring
        if device.isInputConnected && device.isOutputConnected {
            return "✅ Active & Connected"
        } else if !device.isInputConnected && !device.isOutputConnected {
            return "⭕ Disconnected"
        } else {
            return "\(device.isInputConnected ? "🔴 Active (IN)" : "") \(device.isOutputConnected ? "🔵 Active (OUT)" : "")".trimmingCharacters(in: .whitespaces)
        }
    }
    private var cancellables = Set<AnyCancellable>()

    init() {
        os_log("DeviceManager initialized", log: .default, type: .info)
        loadDevices()
        startMonitoring()
    }

    func loadDevices() {
        self.devices = CoreAudioHALManager.shared.enumerateDevices().map { deviceInfo in
            AudioDevice(
                id: UUID(),
                deviceID: deviceInfo.deviceID,
                name: deviceInfo.name,
                manufacturer: deviceInfo.manufacturer,
                inputChannels: deviceInfo.inputChannels,
                outputChannels: deviceInfo.outputChannels,
                sampleRate: deviceInfo.sampleRate,
                isVirtual: deviceInfo.isVirtual,
                isAggregate: deviceInfo.isAggregate
            )
        }
        self.systemConflicts = CoreAudioHALManager.shared.runHealthCheck()
    }

    private func startMonitoring() {
        NotificationCenter.default.publisher(for: .audioDeviceListChanged)
            .sink { [weak self] _ in
                self?.loadDevices()
            }
            .store(in: &cancellables)
        
        Timer.publish(every: 5, on: .main, in: .common).autoconnect()
            .sink { [weak self] _ in
                self?.loadDevices()
            }
            .store(in: &cancellables)
    }
}
