#!/bin/bash

# WavyAudio Build Script - WWDC 2025 Edition
# Optimized for Xcode 26 and Swift 6.2 with new WWDC 2025 features

set -e  # Exit on any error

echo "🎵 WavyAudio Build Script - WWDC 2025 Edition"
echo "================================================"
echo "Swift 6.2 | Xcode 26 | iOS 26.0+ | macOS 26.0+"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check for Xcode 26
print_status "Checking Xcode version..."
XCODE_VERSION=$(xcodebuild -version | head -n 1 | awk '{print $2}')
if [[ "$XCODE_VERSION" < "26.0" ]]; then
    print_warning "Xcode 26.0+ recommended for WWDC 2025 features. Current: $XCODE_VERSION"
else
    print_success "Xcode $XCODE_VERSION detected"
fi

# Check for Swift 6.2
print_status "Checking Swift version..."
SWIFT_VERSION=$(swift --version | head -n 1 | awk '{print $4}')
if [[ "$SWIFT_VERSION" < "6.2" ]]; then
    print_warning "Swift 6.2+ recommended for WWDC 2025 features. Current: $SWIFT_VERSION"
else
    print_success "Swift $SWIFT_VERSION detected"
fi

# Clean previous builds
print_status "Cleaning previous builds..."
xcodebuild clean -project WavyAudio.xcodeproj -scheme WavyAudio

# Note: WavyCore Swift Package is built automatically by Xcode
print_status "WavyCore Swift Package will be built automatically by Xcode..."
print_success "WavyCore package configuration ready"

# Build main application with WWDC 2025 optimizations
print_status "Building WavyAudio with Xcode 26 optimizations..."
xcodebuild build -project WavyAudio.xcodeproj \
                 -scheme WavyAudio \
                 -configuration Release \
                 -destination "platform=macOS,arch=arm64" \
                 SWIFT_VERSION=6.2 \
                 SWIFT_UPCOMING_FEATURE_STRICT_CONCURRENCY=YES \
                 SWIFT_UPCOMING_FEATURE_APPROACHABLE_CONCURRENCY=YES \
                 SWIFT_UPCOMING_FEATURE_DEFAULT_ACTOR_ISOLATION=YES \
                 SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY=YES \
                 ENABLE_EXPLICITLY_BUILT_MODULES=YES \
                 ENABLE_SWIFT_BUILD_SYSTEM=YES

print_success "WavyAudio built successfully with WWDC 2025 features!"

# Optional: Run tests with Swift 6.2
if [[ "$1" == "--test" ]]; then
    print_status "Running tests with Swift 6.2 concurrency..."
    xcodebuild test -project WavyAudio.xcodeproj \
                    -scheme WavyAudio \
                    -destination "platform=macOS,arch=arm64" \
                    SWIFT_VERSION=6.2
    print_success "All tests passed!"
fi

# Optional: Archive for distribution
if [[ "$1" == "--archive" ]]; then
    print_status "Creating archive with Xcode 26 optimizations..."
    xcodebuild archive -project WavyAudio.xcodeproj \
                       -scheme WavyAudio \
                       -configuration Release \
                       -destination "platform=macOS,arch=arm64" \
                       -archivePath "./build/WavyAudio.xcarchive" \
                       SWIFT_VERSION=6.2
    print_success "Archive created at ./build/WavyAudio.xcarchive"
fi

echo ""
print_success "🎉 Build completed successfully!"
echo ""
echo "WWDC 2025 Features Enabled:"
echo "✅ Swift 6.2 with Strict Concurrency"
echo "✅ Approachable Concurrency (single-threaded by default)"
echo "✅ Default Actor Isolation (MainActor)"
echo "✅ Member Import Visibility"
echo "✅ Explicitly Built Modules (faster debugging)"
echo "✅ Swift Build System integration"
echo ""
echo "Ready for iOS 26.0+ and macOS 26.0+ deployment!"
echo "Compatible with Liquid Glass design system."