#!/usr/bin/env python3

import re
import os
import io
import sys
import argparse
from pathlib import Path
from markdown import markdown

def search_transcripts(search_term, transcript_dirs=None):
    """Search for a term across all WWDC transcript files"""
    if transcript_dirs is None:
        # Default search locations
        transcript_dirs = [
            Path.home() / "Desktop" / "wwdc-transcripts",
            Path.home() / "Documents" / "wwdc-transcripts", 
            Path("/usr/local/share/wwdc-transcripts"),
            Path.cwd()
        ]
    
    results = []
    search_pattern = re.compile(search_term, re.IGNORECASE)
    
    for transcript_dir in transcript_dirs:
        if not transcript_dir.exists():
            continue
            
        for transcript_file in transcript_dir.rglob("*.md"):
            try:
                with open(transcript_file, 'r', encoding='utf8') as f:
                    content = f.read()
                    
                matches = search_pattern.findall(content)
                if matches:
                    results.append({
                        'file': transcript_file,
                        'matches': len(matches),
                        'content': content
                    })
            except Exception as e:
                print(f"Error reading {transcript_file}: {e}")
    
    return results

def parse_wwdc_transcript(file_path):
    if not os.path.exists(file_path):
        print("Transcript file not found.")
        sys.exit(1)
    
    transcript_content = ""
    with io.open(file_path, 'r', encoding='utf8') as f:
        transcript_content = f.read()

    # Convert markdown to HTML for better parsing
    html_content = markdown(transcript_content)
    
    return html_content

def analyze_transcript_for_search(content, search_term):
    """Analyze transcript content for search term context"""
    lines = content.split('\n')
    search_pattern = re.compile(search_term, re.IGNORECASE)
    
    matches = []
    for i, line in enumerate(lines):
        if search_pattern.search(line):
            # Get context around the match
            start = max(0, i-2)
            end = min(len(lines), i+3)
            context = '\n'.join(lines[start:end])
            matches.append({
                'line_number': i+1,
                'line': line.strip(),
                'context': context
            })
    
    return matches

def analyze_transcript_for_errors(html_content):
    # Parse HTML for relevant sections related to SwiftUI environment management
    # Placeholder for regex pattern
    pattern = re.compile(r'<\s*h[2-3].*?>(.*?)<\s*\/h[2-3]>', re.IGNORECASE)
    headings = pattern.findall(html_content)

    # Filtering out relevant sections with keywords
    swiftui_sections = [heading for heading in headings if 'swiftui' in heading.lower()]
    
    return swiftui_sections

def apply_fix(file_path, transcript_content):
    # Analyzing code blocks in markdown transcript for SwiftUI patterns
    code_blocks = re.findall(r'```(.*?)```', transcript_content, re.DOTALL)
    for code_block in code_blocks:
        function_pattern = re.compile(r'func\s+(\w+)\(')
        function_match = function_pattern.search(code_block)
        if function_match:
            func_name = function_match.group(1)
            print(f"Applying {func_name} fix to {file_path}...")
            # Placeholder for code modification
            # Example: injecting environment check or lifecycle manager validation
            insert_environment_check(file_path, func_name)

def insert_environment_check(file_path, function_name):
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return
    
    with io.open(file_path, 'r', encoding='utf8') as f:
        file_content = f.read()

    file_content = re.sub(
        rf"func {function_name}\(",
        rf"// WWDC SAFETY CHECK: Ensure \(function_name) is in the main environment context\nfunc {function_name}(", 
        file_content,
        flags=re.MULTILINE
    )

    with io.open(file_path, 'w', encoding='utf8') as f:
        f.write(file_content)

    print(f"Recommended adding environment check to function {function_name} in {file_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='WWDC Transcript Search and Analysis Tool')
    parser.add_argument('--search-transcripts', '--search', 
                        help='Search for a term across all WWDC transcripts')
    parser.add_argument('--transcript', 
                        help='Path to specific transcript file to analyze')
    parser.add_argument('--app-file', 
                        help='Swift file to apply fixes to')
    parser.add_argument('--dirs', nargs='+',
                        help='Additional directories to search for transcripts')
    
    args = parser.parse_args()
    
    if args.search_transcripts:
        print(f"Searching for '{args.search_transcripts}' in WWDC transcripts...")
        
        transcript_dirs = [Path(d) for d in args.dirs] if args.dirs else None
        results = search_transcripts(args.search_transcripts, transcript_dirs)
        
        if not results:
            print("No matches found.")
            print("Searched in:")
            default_dirs = [
                Path.home() / "Desktop" / "wwdc-transcripts",
                Path.home() / "Documents" / "wwdc-transcripts", 
                Path("/usr/local/share/wwdc-transcripts"),
                Path.cwd()
            ]
            for d in (transcript_dirs or default_dirs):
                if d.exists():
                    print(f"  ✓ {d}")
                else:
                    print(f"  ✗ {d} (not found)")
        else:
            print(f"\nFound {len(results)} transcript(s) with matches:\n")
            
            for result in results:
                print(f"📄 {result['file'].name} ({result['matches']} matches)")
                print(f"   Path: {result['file']}")
                
                # Show context for matches
                matches = analyze_transcript_for_search(result['content'], args.search_transcripts)
                for match in matches[:3]:  # Show first 3 matches
                    print(f"   Line {match['line_number']}: {match['line'][:100]}...")
                
                if len(matches) > 3:
                    print(f"   ... and {len(matches) - 3} more matches")
                print()
    
    elif args.transcript:
        transcript_path = args.transcript
        if os.path.isfile(transcript_path):
            print("Processing WWDC transcript...")
            transcript = parse_wwdc_transcript(transcript_path)
            relevant_sections = analyze_transcript_for_errors(transcript)
            print("Relevant WWDC transcript sections:")
            for section in relevant_sections:
                print(f"- {section}")

            if args.app_file and os.path.isfile(args.app_file):
                print(f"Applying automatic fix to {args.app_file}")
                apply_fix(args.app_file, transcript)
        else:
            print(f"Transcript file not found: {transcript_path}")
    
    else:
        parser.print_help()
        print("\nExamples:")
        print("  python3 wwdc-transcript-tool.py --search-transcripts 'audio'")
        print("  python3 wwdc-transcript-tool.py --search 'SwiftUI environment'")
        print("  python3 wwdc-transcript-tool.py --transcript session.md --app-file App.swift")
