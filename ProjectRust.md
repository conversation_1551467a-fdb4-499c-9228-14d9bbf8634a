# Project: ark-loopback - Rust Audio Routing Engine

This document outlines the development plan for the `ark-loopback` Rust audio routing engine. This engine will provide the core audio processing capabilities for the WavyAudio Swift UI application.

## 1. Project Overview

`ark-loopback` will be a Rust library that provides a high-performance, low-latency audio routing engine for macOS. It will be responsible for:

*   Discovering and enumerating all available audio devices on the system.
*   Creating and managing audio routes between devices.
*   Processing audio data in real-time with minimal overhead.
*   Exposing a C-compatible API for interoperability with the Swift UI application.

The engine will be built with a focus on performance, safety, and reliability, leveraging the strengths of the Rust programming language.

## 2. Build and Run Instructions

### Prerequisites

*   Rust 1.70+
*   `cargo` build tool

### Building the Library

1.  **Navigate to the `ark-loopback` directory:**
    ```bash
    cd ark-loopback
    ```
2.  **Build the library in release mode:**
    ```bash
    cargo build --release
    ```
    This will produce a static library file (`libark_loopback.a`) in the `target/release` directory.

### Running the Command-Line Interface (CLI)

The project also includes a command-line interface for testing and debugging purposes.

1.  **Build the CLI:**
    ```bash
    cargo build --release
    ```
2.  **Run the CLI with a specific command:**
    ```bash
    ./target/release/ark list
    ```

## 3. Architecture

The engine will be built using the `coreaudio-rs` library for direct access to the Core Audio HAL (Hardware Abstraction Layer). This will allow for fine-grained control over audio devices and low-level audio processing.

### Key Components

*   **`AudioRouter`:** The main struct that manages audio devices and routes.
*   **`AudioDevice`:** A struct that represents an audio device.
*   **`AudioRoute`:** A struct that represents a connection between two audio devices.
*   **C-compatible API:** A set of `extern "C"` functions that expose the engine's functionality to the Swift UI application.

## 4. Rust Best Practices

The development of `ark-loopback` will adhere to the following Rust best practices:

*   **Error Handling:** Use the `Result` type and the `anyhow` library for robust error handling.
*   **Concurrency:** Use `async/await` and `tokio` for managing asynchronous operations, such as device scanning and audio processing.
*   **Memory Safety:** Leverage Rust's ownership and borrowing rules to prevent common memory-related bugs.
*   **Code Style:** Follow the official Rust style guidelines and use `rustfmt` to ensure consistent code formatting.
*   **Testing:** Write unit tests and integration tests to ensure the correctness and reliability of the engine.

## 5. Swift/Rust Interoperability

The `ark-loopback` library will expose a C-compatible API that can be called from the Swift UI application. This will be achieved using the `cbindgen` tool to automatically generate a C header file from the Rust code.

This will allow the Swift UI application to:

*   Get a list of available audio devices.
*   Create and manage audio routes.
*   Get the status of active routes.

By combining the strengths of Rust and Swift, we can build a powerful and reliable audio routing application for macOS.
