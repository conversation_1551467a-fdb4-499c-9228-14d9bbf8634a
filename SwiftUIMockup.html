<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WavyAudio Mockup</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; background-color: #f6f8fa; margin: 0; }
        .container { display: flex; height: 100vh; }
        .sidebar { width: 250px; background-color: #2c3e50; color: white; padding: 20px; }
        .sidebar h2 { font-size: 1.2em; margin-bottom: 20px; }
        .device-list { list-style: none; padding: 0; }
        .device-list-item { padding: 10px; border-radius: 5px; margin-bottom: 10px; background-color: #34495e; }
        .main-content { flex-grow: 1; padding: 20px; }
        .canvas { width: 100%; height: 70%; background-color: #ecf0f1; border: 1px solid #bdc3c7; border-radius: 5px; margin-bottom: 20px; }
        .status-bar { display: flex; justify-content: space-between; align-items: center; }
        .status-indicator { padding: 10px 20px; background-color: #27ae60; color: white; border-radius: 5px; }
        .presets-panel { background-color: #ecf0f1; padding: 20px; border-radius: 5px; }
        .presets-panel h3 { margin-top: 0; }
        .preset-button { padding: 10px 20px; background-color: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h2>Audio Devices</h2>
            <ul class="device-list">
                <li class="device-list-item">MacBook Pro Speakers</li>
                <li class="device-list-item">External Headphones</li>
                <li class="device-list-item">Zoom Audio Device</li>
                <li class="device-list-item">DJPro Virtual Output</li>
            </ul>
        </div>
        <div class="main-content">
            <h1>Audio Routing Canvas</h1>
            <div class="canvas">
                <!-- Visual routing graph will be displayed here -->
            </div>
            <div class="status-bar">
                <div class="status-indicator">System Status: Active</div>
                <div class="presets-panel">
                    <h3>Quick Presets</h3>
                    <button class="preset-button">DJPro to Zoom</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
