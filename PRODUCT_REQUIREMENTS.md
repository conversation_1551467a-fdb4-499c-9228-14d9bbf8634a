### **Product Requirements Document: WavyAudio**

### 1. Introduction & Vision

**Product Name:** WavyAudio (incorporating the "Ark" audio engine)

**Vision:** To create a premier audio routing and management application for macOS that offers unparalleled flexibility, performance, and ease of use. WavyAudio will empower musicians, streamers, podcasters, and audio professionals to effortlessly route audio between applications and hardware devices, breaking the limitations of standard OS audio controls.

**Core Problem:** macOS provides limited and often cumbersome tools for inter-application audio routing. Users who need to send audio from a music app to a streaming app, or from a DAW to a communication tool like Zoom, must rely on complex, often unstable, third-party solutions. WavyAudio aims to solve this by providing a robust, reliable, and intuitive interface for all audio routing needs.

### 2. Target Audience

*   **Musicians & Producers:** For routing DAWs (Digital Audio Workstations) like Ableton or Logic to other apps, syncing tempo via IAC, and managing complex virtual instrument setups.
*   **Streamers & Content Creators:** For mixing game audio, music, and microphone inputs into a single stream for platforms like Twitch or YouTube.
*   **Podcasters:** For recording interviews with multiple remote guests and local audio sources.
*   **Audio Professionals & Enthusiasts:** For advanced audio setups, device management, and system-wide audio monitoring.

### 3. Technical Stack

*   **Backend Audio Engine:** Rust, utilizing the `coreaudio-rs` library for high-performance, safe, and low-latency access to the macOS Core Audio framework.
*   **Frontend UI:** Swift and SwiftUI for a modern, native, and responsive user experience on macOS.
*   **Inter-process Communication:** A Swift-Rust bridge to ensure seamless communication between the UI and the backend audio engine.

### 4. Features List

#### **MVP (Minimum Viable Product) Features**

*   **F1: Audio Device Discovery**
    *   **F1.1:** Automatically scan and detect all available audio devices on startup, including hardware inputs/outputs and virtual devices.
    *   **F1.2:** Display a comprehensive list of all detected devices in a clear, organized sidebar.
    *   **F1.3:** Show key device properties: Name, Manufacturer, Input/Output channel count, and current Sample Rate.
    *   **F1.4:** Implement a "Rescan" button to manually refresh the device list.

*   **F2: Core Audio Routing**
    *   **F2.1:** Create simple, direct audio routes from one device's output to another device's input.
    *   **F2.2:** Support for multi-channel audio passthrough.
    *   **F2.3:** A visual representation of active routes (e.g., on a canvas or list).
    *   **F2.4:** Ability to activate and deactivate individual routes with a single click.
    *   **F2.5:** Ability to stop all active routing at once.

*   **F3: Virtual "Ark" Audio Device**
    *   **F3.1:** Provide at least one virtual audio device (e.g., "Ark Loopback") that users can select as an input or output in any macOS application.
    *   **F3.2:** This device will serve as the primary hub for complex routing configurations.

*   **F4: Basic User Interface**
    *   **F4.1:** A main window with a device list sidebar and a central area for managing routes.
    *   **F4.2:** Clear visual indicators for active routes and device status.
    *   **F4.3:** System status indicator in the menu bar or main window.

#### **Post-MVP / Advanced Features**

*   **F5: Advanced Routing Canvas**
    *   **F5.1:** A fully interactive, node-based canvas (`AudioRoutingCanvas.swift`) where users can drag-and-drop devices and draw connections between them.
    *   **F5.2:** Visual feedback on the canvas for audio levels (e.g., animated, glowing connections).

*   **F6: Preset Management**
    *   **F6.1:** Save complex routing configurations as named presets (e.g., "Streaming Setup," "Podcast Recording").
    *   **F6.2:** A quick-access panel (`QuickPresetPanel.swift`) to load presets instantly.
    *   **F6.3:** Ship the application with pre-built presets for common use cases (e.g., "DJ to Zoom," "Waveform to DJ").

*   **F7: Inter-App Communication (IAC) Sync**
    *   **F7.1:** Detect and utilize the macOS IAC Driver for sending MIDI beat clock information.
    *   **F7.2:** Allow users to designate a "master" application and sync the tempo of other "slave" applications to it.

*   **F8: UI/UX Enhancements**
    *   **F8.1:** Search and filtering functionality for the device list (`SearchField.swift`).
    *   **F8.2:** UI performance optimization using Metal (`MetalFXOptimizer.swift`) for a fluid and responsive experience, especially on the routing canvas.
    *   **F8.3:** Detailed view for each device, showing all its properties and allowing for configuration where possible.

*   **F9: Audio Processing & Effects**
    *   **F9.1:** (Future) Ability to insert basic audio processing units (e.g., Gain, EQ, Compressor) into a route.
    *   **F9.2:** (Future) Support for hosting third-party AU (Audio Unit) plugins.

### 5. Future Considerations

*   **Windows Support:** The Rust backend is cross-platform, opening the possibility for a future Windows version.
*   **Mobile Companion App:** A remote control app for iOS/iPadOS to manage routes and presets.
*   **Scripting/Automation:** Exposing the routing engine via an API or scripting interface for power users.
