#!/bin/bash

# WavyAudio File Watcher and Auto-Build Script
# Monitors Swift files for changes and triggers optimized builds

set -e

# Configuration
PROJECT_DIR="/Users/<USER>/Desktop/NewAudio/WavyAudio"
WATCH_PATHS="$PROJECT_DIR/WavyAudio"
BUILD_LOG="$PROJECT_DIR/build.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to run optimized build
build_project() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] Building WavyAudio...${NC}"
    
    cd "$PROJECT_DIR"
    
    # Run optimized build
    if xcodebuild -project WavyAudio.xcodeproj -scheme WavyAudio -configuration Debug build \
        COMPILER_INDEX_STORE_ENABLE=NO \
        ONLY_ACTIVE_ARCH=YES \
        > "$BUILD_LOG" 2>&1; then
        echo -e "${GREEN}[$(date '+%H:%M:%S')] ✅ Build succeeded${NC}"
        return 0
    else
        echo -e "${RED}[$(date '+%H:%M:%S')] ❌ Build failed${NC}"
        echo -e "${YELLOW}Last 5 lines of build log:${NC}"
        tail -5 "$BUILD_LOG" | sed 's/^/  /'
        return 1
    fi
}

# Function to handle file changes
on_file_change() {
    local changed_file="$1"
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] File changed: $(basename "$changed_file")${NC}"
    
    # Brief delay to allow file operations to complete
    sleep 0.3
    
    # Trigger build
    build_project
}

# Initial build
echo -e "${BLUE}🎵 Starting WavyAudio file watcher...${NC}"
echo -e "${BLUE}📁 Watching: $WATCH_PATHS${NC}"
echo -e "${BLUE}📋 Build logs: $BUILD_LOG${NC}"
echo ""

# Run initial build
build_project
echo ""

# Start file watching
echo -e "${BLUE}[$(date '+%H:%M:%S')] 👀 Watching for changes... (Press Ctrl+C to stop)${NC}"

fswatch -1 \
    --include='\.swift$' \
    --include='\.h$' \
    --include='\.metal$' \
    --exclude='DerivedData' \
    --exclude='\.build' \
    --exclude='\.git' \
    --exclude='build\.log' \
    "$WATCH_PATHS" | while read changed_file; do
    
    on_file_change "$changed_file"
    
    # Wait a bit before watching for next change to avoid build spam
    sleep 1
done