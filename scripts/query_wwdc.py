
import chromadb
import argparse
from chromadb.utils import embedding_functions
import os

# --- Configuration ---
DB_PATH = os.path.expanduser("~/.config/wavy/mcp_knowledge/chroma_db")
COLLECTION_NAME = "wwdc_transcripts"
EMBEDDING_MODEL = "nomic-embed-text"
OLLAMA_BASE_URL = "http://localhost:11434"

# --- Main Query Logic ---

def query_collection(query_text, n_results=5):
    """
    Queries the ChromaDB collection and returns the most relevant document chunks.
    """
    if not os.path.exists(DB_PATH):
        print(f"Error: Database path not found at {DB_PATH}")
        print("Please run the ingest_wwdc.py script first.")
        return

    print(f"Querying for: '{query_text}'")
    print(f"Using embedding model: {EMBEDDING_MODEL}")

    # 1. Initialize ChromaDB client and collection
    client = chromadb.PersistentClient(path=DB_PATH)
    
    ollama_ef = embedding_functions.OllamaEmbeddingFunction(
        url=f"{OLLAMA_BASE_URL}/api/embeddings",
        model_name=EMBEDDING_MODEL,
    )

    try:
        collection = client.get_collection(
            name=COLLECTION_NAME,
            embedding_function=ollama_ef
        )
    except ValueError:
        print(f"Error: Collection '{COLLECTION_NAME}' not found.")
        print("Please ensure you have ingested data first.")
        return

    # 2. Perform the query
    results = collection.query(
        query_texts=[query_text],
        n_results=n_results
    )

    # 3. Print the results
    if not results or not results.get('documents') or not results['documents'][0]:
        print("No relevant documents found.")
        return

    print("\n--- Top Results ---\n")
    for i, doc in enumerate(results['documents'][0]):
        metadata = results['metadatas'][0][i]
        distance = results['distances'][0][i]
        source_file = metadata.get('source', 'Unknown')
        
        print(f"Result {i+1} (Source: {os.path.basename(source_file)}, Distance: {distance:.4f}):")
        print("-" * 20)
        print(doc)
        print("\n")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="WWDC Transcript Query Tool for WavyAudio MCP")
    parser.add_argument(
        "query",
        type=str,
        nargs='+',
        help="The query text to search for."
    )
    parser.add_argument(
        "-n", "--n_results",
        type=int,
        default=5,
        help="Number of results to return."
    )
    args = parser.parse_args()
    
    # Join the query parts into a single string
    full_query = " ".join(args.query)
    
    query_collection(full_query, args.n_results)
