
import os
import chromadb
from chromadb.utils import embedding_functions
import argparse
import hashlib

# --- Configuration ---
# Use a consistent path for the database
DB_PATH = os.path.expanduser("~/.config/wavy/mcp_knowledge/chroma_db")
COLLECTION_NAME = "wwdc_transcripts"
# Your chosen embedding model
EMBEDDING_MODEL = "nomic-embed-text" 
# The base URL for your local Ollama instance
OLLAMA_BASE_URL = "http://localhost:11434"

# --- Helper Functions ---

def get_file_hash(filepath):
    """Computes the SHA256 hash of a file to detect changes."""
    h = hashlib.sha256()
    with open(filepath, 'rb') as file:
        while True:
            # Read file in chunks
            chunk = file.read(h.block_size)
            if not chunk:
                break
            h.update(chunk)
    return h.hexdigest()

def chunk_text(text, chunk_size=1000, overlap=200):
    """Splits text into overlapping chunks."""
    chunks = []
    start = 0
    while start < len(text):
        end = start + chunk_size
        chunks.append(text[start:end])
        start += chunk_size - overlap
    return chunks

# --- Main Ingestion Logic ---

def ingest_files(directory_path, force_reingest=False):
    """
    Ingests all markdown files from a directory into the ChromaDB collection.
    Chunks files, generates embeddings, and stores them.
    Skips files that have not changed since the last ingestion unless force_reingest is True.
    """
    print(f"Starting ingestion from: {directory_path}")
    print(f"Database path: {DB_PATH}")
    print(f"Using embedding model: {EMBEDDING_MODEL}")

    # 1. Initialize ChromaDB client and collection
    client = chromadb.PersistentClient(path=DB_PATH)
    
    # Use Ollama for embeddings
    ollama_ef = embedding_functions.OllamaEmbeddingFunction(
        url=f"{OLLAMA_BASE_URL}/api/embeddings",
        model_name=EMBEDDING_MODEL,
    )

    collection = client.get_or_create_collection(
        name=COLLECTION_NAME,
        embedding_function=ollama_ef
    )
    
    # 2. Track ingested files to avoid re-processing
    # This is a simple file-based tracking mechanism
    processed_hashes_file = os.path.join(DB_PATH, "processed_hashes.txt")
    processed_hashes = {}
    if not force_reingest and os.path.exists(processed_hashes_file):
        with open(processed_hashes_file, "r") as f:
            for line in f:
                parts = line.strip().split(":", 1)
                if len(parts) == 2:
                    processed_hashes[parts[0]] = parts[1]

    # 3. Walk through the directory and process markdown files
    for root, _, files in os.walk(directory_path):
        for filename in files:
            if filename.endswith(".md"):
                filepath = os.path.join(root, filename)
                file_hash = get_file_hash(filepath)
                
                # Check if the file has been processed and hasn't changed
                if not force_reingest and processed_hashes.get(filepath) == file_hash:
                    print(f"Skipping unchanged file: {filename}")
                    continue

                print(f"Processing: {filename}...")
                try:
                    with open(filepath, "r", encoding="utf-8") as f:
                        content = f.read()

                    # Split content into manageable chunks
                    chunks = chunk_text(content)
                    
                    # Generate unique IDs for each chunk
                    chunk_ids = [f"{filepath}_{i}" for i in range(len(chunks))]

                    # Add chunks to the collection
                    collection.add(
                        documents=chunks,
                        metadatas=[{"source": filepath} for _ in chunks],
                        ids=chunk_ids
                    )
                    
                    # Update the hash in our tracking dictionary
                    processed_hashes[filepath] = file_hash
                    print(f"  > Ingested {len(chunks)} chunks.")

                except Exception as e:
                    print(f"  > Error processing {filename}: {e}")

    # 4. Save the updated hashes to the tracking file
    with open(processed_hashes_file, "w") as f:
        for filepath, file_hash in processed_hashes.items():
            f.write(f"{filepath}:{file_hash}\n")
            
    print("\nIngestion complete!")
    print(f"Total documents in collection: {collection.count()}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="WWDC Transcript Ingestion Tool for WavyAudio MCP")
    parser.add_argument(
        "directory",
        type=str,
        help="The directory containing WWDC transcript markdown files."
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force re-ingestion of all files, even if they haven't changed."
    )
    args = parser.parse_args()
    
    ingest_files(args.directory, args.force)
