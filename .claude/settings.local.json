{"permissions": {"allow": ["Bash(node:*)", "Bash(npm --version)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(swift --version)", "Bash(sudo xcode-select:*)", "Bash(xcode-select:*)", "Bash(git -C /Users/<USER>/Desktop/LiquidGlass status)", "Bash(git -C /Users/<USER>/Desktop/LiquidGlass commit -m \"Save current state before Apple Liquid Glass API corrections\n\n- Preserve existing glass effect implementations\n- Multiple view files with custom glass styles\n- Ready for Apple documentation compliance updates\n\n🤖 Generated with [<PERSON> Code](https://claude.ai/code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\")", "Bash(rm:*)", "Bash(xcodebuild clean:*)", "Bash(xcodebuild:*)", "WebFetch(domain:developer.apple.com)", "WebFetch(domain:www.swift.org)", "Bash(cp:*)", "<PERSON><PERSON>(open:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(xcrun:*)", "<PERSON><PERSON>(python3:*)"], "deny": []}}