# Project: WavyAudio - Swift UI Audio Routing App

This document outlines the development plan for the WavyAudio Swift UI application. This app will provide a user-friendly interface for managing audio routing on macOS, leveraging the powerful audio processing capabilities of the Rust-based `ark-loopback` engine.

## 1. Project Overview

WavyAudio will be a native macOS application built with Swift UI. It will provide a visual interface for users to:

*   View all available audio devices on the system.
*   Create and manage custom audio routes between devices.
*   Use pre-configured routing presets for common scenarios (e.g., "DJPro to Zoom").
*   Monitor the status of active audio routes.

The application will be designed with a clean, modern, and intuitive user interface, following Apple's Human Interface Guidelines.

## 2. Build and Run Instructions

### Prerequisites

*   macOS 12.0+
*   Xcode 14.0+
*   Swift 5.7+

### Building the Application

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd WavyAudio
    ```
2.  **Open the project in Xcode:**
    ```bash
    xed .
    ```
3.  **Select the "WavyAudio" scheme** and a target device (e.g., "My Mac").
4.  **Build the project:** Press `Cmd+B` or select "Product" > "Build" from the menu.
5.  **Run the application:** Press `Cmd+R` or select "Product" > "Run" from the menu.

## 3. Application Architecture

The application will follow the Model-View-ViewModel (MVVM) design pattern to ensure a clean separation of concerns.

*   **Models:** These will represent the data structures of the application, such as `AudioDevice` and `AudioRoute`. These models will be shared between the Swift UI app and the Rust backend.
*   **Views:** These will be the UI components of the application, built with Swift UI. They will be responsible for displaying data to the user and capturing user input.
*   **ViewModels:** These will act as the bridge between the Models and the Views. They will contain the business logic of the application, such as fetching the list of audio devices, creating and managing audio routes, and updating the UI in response to changes in the data.

## 4. Core Components

### 4.1. Main Content View

This will be the main window of the application. It will contain the following components:

*   **Device List Sidebar:** A list of all available audio devices, categorized by input and output.
*   **Audio Routing Canvas:** A visual representation of the audio routing graph, where users can create and manage routes by connecting devices.
*   **System Status Indicator:** An indicator that shows the overall status of the audio routing system.

### 4.2. Device List Sidebar

This view will display a list of all available audio devices. Each device will be represented by a `DeviceRowView`.

### 4.3. Audio Routing Canvas

This will be the core of the application's UI. It will allow users to:

*   Drag and drop devices from the sidebar onto the canvas.
*   Create connections between devices to represent audio routes.
*   View the status of each route (e.g., active, inactive, error).

### 4.4. Quick Preset Panel

This panel will provide a list of pre-configured routing presets that users can apply with a single click.

## 5. Swift/Rust Interoperability

The Swift UI application will communicate with the Rust `ark-loopback` engine to perform audio routing operations. This will be achieved using a Swift/Rust bridge.

*   **Swift side:** A Swift `AudioEngineManager` class will be responsible for calling the Rust functions.
*   **Rust side:** The `ark-loopback` library will expose a C-compatible API that can be called from Swift.

This separation of concerns will allow us to leverage the performance and safety of Rust for the audio processing while providing a modern and responsive user interface with Swift UI.
