// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		311DC2142E0E8F1400F16E70 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 311DC1FE2E0E8F1100F16E70 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 311DC2052E0E8F1100F16E70;
			remoteInfo = Proaudio;
		};
		311DC21E2E0E8F1400F16E70 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 311DC1FE2E0E8F1100F16E70 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 311DC2052E0E8F1100F16E70;
			remoteInfo = Proaudio;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		311DC2062E0E8F1100F16E70 /* Proaudio.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Proaudio.app; sourceTree = BUILT_PRODUCTS_DIR; };
		311DC2132E0E8F1400F16E70 /* ProaudioTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ProaudioTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		311DC21D2E0E8F1400F16E70 /* ProaudioUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ProaudioUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		311DC2082E0E8F1100F16E70 /* Proaudio */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Proaudio;
			sourceTree = "<group>";
		};
		311DC2162E0E8F1400F16E70 /* ProaudioTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ProaudioTests;
			sourceTree = "<group>";
		};
		311DC2202E0E8F1400F16E70 /* ProaudioUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ProaudioUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		311DC2032E0E8F1100F16E70 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		311DC2102E0E8F1400F16E70 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		311DC21A2E0E8F1400F16E70 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		311DC1FD2E0E8F1100F16E70 = {
			isa = PBXGroup;
			children = (
				311DC2082E0E8F1100F16E70 /* Proaudio */,
				311DC2162E0E8F1400F16E70 /* ProaudioTests */,
				311DC2202E0E8F1400F16E70 /* ProaudioUITests */,
				311DC2072E0E8F1100F16E70 /* Products */,
			);
			sourceTree = "<group>";
		};
		311DC2072E0E8F1100F16E70 /* Products */ = {
			isa = PBXGroup;
			children = (
				311DC2062E0E8F1100F16E70 /* Proaudio.app */,
				311DC2132E0E8F1400F16E70 /* ProaudioTests.xctest */,
				311DC21D2E0E8F1400F16E70 /* ProaudioUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		311DC2052E0E8F1100F16E70 /* Proaudio */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 311DC2272E0E8F1400F16E70 /* Build configuration list for PBXNativeTarget "Proaudio" */;
			buildPhases = (
				311DC2022E0E8F1100F16E70 /* Sources */,
				311DC2032E0E8F1100F16E70 /* Frameworks */,
				311DC2042E0E8F1100F16E70 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				311DC2082E0E8F1100F16E70 /* Proaudio */,
			);
			name = Proaudio;
			packageProductDependencies = (
			);
			productName = Proaudio;
			productReference = 311DC2062E0E8F1100F16E70 /* Proaudio.app */;
			productType = "com.apple.product-type.application";
		};
		311DC2122E0E8F1400F16E70 /* ProaudioTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 311DC22A2E0E8F1400F16E70 /* Build configuration list for PBXNativeTarget "ProaudioTests" */;
			buildPhases = (
				311DC20F2E0E8F1400F16E70 /* Sources */,
				311DC2102E0E8F1400F16E70 /* Frameworks */,
				311DC2112E0E8F1400F16E70 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				311DC2152E0E8F1400F16E70 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				311DC2162E0E8F1400F16E70 /* ProaudioTests */,
			);
			name = ProaudioTests;
			packageProductDependencies = (
			);
			productName = ProaudioTests;
			productReference = 311DC2132E0E8F1400F16E70 /* ProaudioTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		311DC21C2E0E8F1400F16E70 /* ProaudioUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 311DC22D2E0E8F1400F16E70 /* Build configuration list for PBXNativeTarget "ProaudioUITests" */;
			buildPhases = (
				311DC2192E0E8F1400F16E70 /* Sources */,
				311DC21A2E0E8F1400F16E70 /* Frameworks */,
				311DC21B2E0E8F1400F16E70 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				311DC21F2E0E8F1400F16E70 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				311DC2202E0E8F1400F16E70 /* ProaudioUITests */,
			);
			name = ProaudioUITests;
			packageProductDependencies = (
			);
			productName = ProaudioUITests;
			productReference = 311DC21D2E0E8F1400F16E70 /* ProaudioUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		311DC1FE2E0E8F1100F16E70 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					311DC2052E0E8F1100F16E70 = {
						CreatedOnToolsVersion = 26.0;
					};
					311DC2122E0E8F1400F16E70 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 311DC2052E0E8F1100F16E70;
					};
					311DC21C2E0E8F1400F16E70 = {
						CreatedOnToolsVersion = 26.0;
						TestTargetID = 311DC2052E0E8F1100F16E70;
					};
				};
			};
			buildConfigurationList = 311DC2012E0E8F1100F16E70 /* Build configuration list for PBXProject "Proaudio" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 311DC1FD2E0E8F1100F16E70;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 311DC2072E0E8F1100F16E70 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				311DC2052E0E8F1100F16E70 /* Proaudio */,
				311DC2122E0E8F1400F16E70 /* ProaudioTests */,
				311DC21C2E0E8F1400F16E70 /* ProaudioUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		311DC2042E0E8F1100F16E70 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		311DC2112E0E8F1400F16E70 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		311DC21B2E0E8F1400F16E70 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		311DC2022E0E8F1100F16E70 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		311DC20F2E0E8F1400F16E70 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		311DC2192E0E8F1400F16E70 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		311DC2152E0E8F1400F16E70 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 311DC2052E0E8F1100F16E70 /* Proaudio */;
			targetProxy = 311DC2142E0E8F1400F16E70 /* PBXContainerItemProxy */;
		};
		311DC21F2E0E8F1400F16E70 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 311DC2052E0E8F1100F16E70 /* Proaudio */;
			targetProxy = 311DC21E2E0E8F1400F16E70 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		311DC2252E0E8F1400F16E70 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		311DC2262E0E8F1400F16E70 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		311DC2282E0E8F1400F16E70 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.Proaudio;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		311DC2292E0E8F1400F16E70 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.Proaudio;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
		311DC22B2E0E8F1400F16E70 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.ProaudioTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Proaudio.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Proaudio";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		311DC22C2E0E8F1400F16E70 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.ProaudioTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Proaudio.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Proaudio";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
		311DC22E2E0E8F1400F16E70 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.ProaudioUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = Proaudio;
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		311DC22F2E0E8F1400F16E70 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = N2V5RLPP86;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = D.ProaudioUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = Proaudio;
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		311DC2012E0E8F1100F16E70 /* Build configuration list for PBXProject "Proaudio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				311DC2252E0E8F1400F16E70 /* Debug */,
				311DC2262E0E8F1400F16E70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		311DC2272E0E8F1400F16E70 /* Build configuration list for PBXNativeTarget "Proaudio" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				311DC2282E0E8F1400F16E70 /* Debug */,
				311DC2292E0E8F1400F16E70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		311DC22A2E0E8F1400F16E70 /* Build configuration list for PBXNativeTarget "ProaudioTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				311DC22B2E0E8F1400F16E70 /* Debug */,
				311DC22C2E0E8F1400F16E70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		311DC22D2E0E8F1400F16E70 /* Build configuration list for PBXNativeTarget "ProaudioUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				311DC22E2E0E8F1400F16E70 /* Debug */,
				311DC22F2E0E8F1400F16E70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 311DC1FE2E0E8F1100F16E70 /* Project object */;
}
