use anyhow::Result;
use clap::{Parser, Subcommand};
use ark_loopback::AudioRouter;

#[derive(Parser)]
#[command(name = "ark")]
#[command(about = "Ark Audio Driver Loopback Router for DJ Applications")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// List available audio devices
    List,
    /// Create DJPro to Zoom loopback
    DjToZoom,
    /// Create Waveform to DJPro routing with IAC sync
    WaveformToDj,
    /// Create custom routing preset
    Route {
        #[arg(short, long)]
        from: String,
        #[arg(short, long)]
        to: String,
        #[arg(short, long)]
        channels: Option<u32>,
    },
    /// Stop all routing
    Stop,
    /// Show current routing status
    Status,
}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    let cli = Cli::parse();
    
    let router = AudioRouter::new().await?;
    
    match cli.command {
        Commands::List => {
            let devices = router.list_devices().await?;
            println!("Available Audio Devices:");
            for device in devices {
                println!("  {} - {} ({} in, {} out)", 
                    device.id, device.name, device.input_channels, device.output_channels);
            }
        }
        
        Commands::DjToZoom => {
            println!("Setting up DJPro → Zoom loopback...");
            router.create_dj_to_zoom_routing().await?;
            println!("✓ DJPro output now routed to Zoom input");
        }
        
        Commands::WaveformToDj => {
            println!("Setting up Waveform → DJPro routing with IAC sync...");
            router.create_waveform_to_dj_routing().await?;
            println!("✓ Waveform routed to DJPro track with IAC sync");
        }
        
        Commands::Route { from, to, channels } => {
            println!("Creating custom route: {} → {}", from, to);
            router.create_custom_route(&from, &to, channels.unwrap_or(2)).await?;
            println!("✓ Custom route created");
        }
        
        Commands::Stop => {
            println!("Stopping all audio routing...");
            router.stop_all_routing().await?;
            println!("✓ All routing stopped");
        }
        
        Commands::Status => {
            let status = router.get_routing_status().await?;
            println!("Current Audio Routing:");
            for route in &status.active_routes {
                println!("  {} → {} ({} channels)", route.from, route.to, route.channels);
            }
            if status.active_routes.is_empty() {
                println!("  No active routes");
            }
        }
    }
    
    Ok(())
}