use anyhow::{anyhow, Result};
use coreaudio::audio_unit::{AudioUnit, Element, IOType, Scope};
use coreaudio::audio_unit::render_callback::{data, Args};
use coreaudio::sys::{AudioObjectPropertyAddress, kAudioHardwarePropertyDevices, kAudioObjectPropertyScopeGlobal, kAudioObjectPropertyElementMaster, kAudioObjectPropertyName, kAudioObjectPropertyManufacturer, kAudioDevicePropertyNominalSampleRate, kAudioDevicePropertyStreamConfiguration, kAudioObjectPropertyScopeInput, kAudioObjectPropertyScopeOutput, kAudioObjectSystemObject, AudioDeviceID, AudioBufferList, AudioObjectGetPropertyData};
use log::info;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::sync::RwLock;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioDevice {
    pub id: u32,
    pub name: String,
    pub manufacturer: String,
    pub input_channels: u32,
    pub output_channels: u32,
    pub sample_rate: f64,
    pub is_ark_device: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioRoute {
    pub from: String,
    pub to: String,
    pub channels: u32,
    pub active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoutingStatus {
    pub active_routes: Vec<AudioRoute>,
    pub ark_devices: Vec<AudioDevice>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoutingPreset {
    pub name: String,
    pub routes: Vec<AudioRoute>,
}

pub struct AudioRouter {
    devices: Arc<RwLock<Vec<AudioDevice>>>,
    active_routes: Arc<Mutex<Vec<AudioRoute>>>,
    audio_units: Arc<Mutex<HashMap<String, AudioUnit>>>,
}

fn cfstring_ref_to_string(cfstring_ref: *const std::ffi::c_void) -> String {
    if cfstring_ref.is_null() {
        return String::new();
    }
    let cfstring_ref = cfstring_ref as coreaudio::sys::CFStringRef;
    let length = unsafe { coreaudio::sys::CFStringGetLength(cfstring_ref) };
    let range = coreaudio::sys::CFRange {
        location: 0,
        length,
    };
    let mut buffer_size = 0;
    unsafe {
        coreaudio::sys::CFStringGetBytes(
            cfstring_ref,
            range,
            coreaudio::sys::kCFStringEncodingUTF8,
            0,
            0u8,
            std::ptr::null_mut(),
            0,
            &mut buffer_size,
        );
    }
    let mut buffer = vec![0u8; buffer_size as usize];
    unsafe {
        coreaudio::sys::CFStringGetBytes(
            cfstring_ref,
            range,
            coreaudio::sys::kCFStringEncodingUTF8,
            0,
            0u8,
            buffer.as_mut_ptr(),
            buffer.len() as i64,
            std::ptr::null_mut(),
        );
    }
    String::from_utf8(buffer).unwrap_or_default()
}

impl AudioRouter {
    pub async fn new() -> Result<Self> {
        info!("Initializing Ark Audio Router");
        
        let router = Self {
            devices: Arc::new(RwLock::new(Vec::new())),
            active_routes: Arc::new(Mutex::new(Vec::new())),
            audio_units: Arc::new(Mutex::new(HashMap::new())),
        };
        
        router.scan_devices().await?;
        Ok(router)
    }
    
    pub async fn list_devices(&self) -> Result<Vec<AudioDevice>> {
        Ok(self.devices.read().await.clone())
    }
    
    pub async fn scan_devices(&self) -> Result<()> {
        info!("Scanning for audio devices...");
    
        let address = AudioObjectPropertyAddress {
            mSelector: kAudioHardwarePropertyDevices,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMaster,
        };
    
        let mut size = 0;
        let status = unsafe {
            AudioObjectGetPropertyData(
                kAudioObjectSystemObject,
                &address,
                0,
                std::ptr::null(),
                &mut size as *mut _ as *mut u32,
                std::ptr::null_mut(),
            )
        };

        if status != coreaudio::sys::noErr as i32 {
            return Err(anyhow!("Failed to get device list size"));
        }

        let mut device_ids: Vec<AudioDeviceID> = vec![0; (size / std::mem::size_of::<AudioDeviceID>()) as usize];
        let status = unsafe {
            AudioObjectGetPropertyData(
                kAudioObjectSystemObject,
                &address,
                0,
                std::ptr::null(),
                &mut size as *mut _ as *mut u32,
                device_ids.as_mut_ptr() as *mut _ as *mut std::ffi::c_void,
            )
        };

        if status != coreaudio::sys::noErr as i32 {
            return Err(anyhow!("Failed to get device IDs"));
        }
    
        let mut devices = Vec::new();
        for device_id in device_ids {
            if let Ok(device) = self.get_device_info(device_id).await {
                devices.push(device);
            }
        }
    
        info!("Found {} audio devices", devices.len());
        *self.devices.write().await = devices;
        Ok(())
    }
    
    async fn get_device_info(&self, device_id: u32) -> Result<AudioDevice> {
        let name = self.get_device_name(device_id)?;
        let manufacturer = self.get_device_manufacturer(device_id)?;
        let sample_rate = self.get_device_sample_rate(device_id)?;
        let input_channels = self.get_device_channel_count(device_id, kAudioObjectPropertyScopeInput)?;
        let output_channels = self.get_device_channel_count(device_id, kAudioObjectPropertyScopeOutput)?;
        
        // Check if this is an Ark device
        let is_ark_device = name.to_lowercase().contains("ark") || 
                           manufacturer.to_lowercase().contains("ark");
        
        Ok(AudioDevice {
            id: device_id,
            name,
            manufacturer,
            input_channels,
            output_channels,
            sample_rate,
            is_ark_device,
        })
    }
    
    fn get_device_name(&self, device_id: u32) -> Result<String> {
        let address = AudioObjectPropertyAddress {
            mSelector: kAudioObjectPropertyName,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMaster,
        };
        let mut size = std::mem::size_of::<*mut std::ffi::c_void>();
        let mut name: *mut std::ffi::c_void = std::ptr::null_mut();
        let status = unsafe {
            AudioObjectGetPropertyData(
                device_id,
                &address,
                0,
                std::ptr::null(),
                &mut size as *mut _ as *mut u32,
                &mut name as *mut _ as *mut std::ffi::c_void,
            )
        };

        if status != coreaudio::sys::noErr as i32 {
            return Err(anyhow!("Failed to get device name"));
        }

        let name_str = cfstring_ref_to_string(name as *const _);
        Ok(name_str)
    }
    
    fn get_device_manufacturer(&self, device_id: u32) -> Result<String> {
        let address = AudioObjectPropertyAddress {
            mSelector: kAudioObjectPropertyManufacturer,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMaster,
        };
        let mut size = std::mem::size_of::<*mut std::ffi::c_void>();
        let mut manufacturer: *mut std::ffi::c_void = std::ptr::null_mut();
        let status = unsafe {
            AudioObjectGetPropertyData(
                device_id,
                &address,
                0,
                std::ptr::null(),
                &mut size as *mut _ as *mut u32,
                &mut manufacturer as *mut _ as *mut std::ffi::c_void,
            )
        };

        if status != coreaudio::sys::noErr as i32 {
            return Err(anyhow!("Failed to get device manufacturer"));
        }

        let manufacturer_str = cfstring_ref_to_string(manufacturer as *const _);
        Ok(manufacturer_str)
    }
    
    fn get_device_sample_rate(&self, device_id: u32) -> Result<f64> {
        let address = AudioObjectPropertyAddress {
            mSelector: kAudioDevicePropertyNominalSampleRate,
            mScope: kAudioObjectPropertyScopeGlobal,
            mElement: kAudioObjectPropertyElementMaster,
        };
        let mut size = std::mem::size_of::<f64>();
        let mut sample_rate: f64 = 0.0;
        let status = unsafe {
            AudioObjectGetPropertyData(
                device_id,
                &address,
                0,
                std::ptr::null(),
                &mut size as *mut _ as *mut u32,
                &mut sample_rate as *mut _ as *mut std::ffi::c_void,
            )
        };

        if status != coreaudio::sys::noErr as i32 {
            return Err(anyhow!("Failed to get device sample rate"));
        }

        Ok(sample_rate)
    }
    
    fn get_device_channel_count(&self, device_id: u32, scope: u32) -> Result<u32> {
        let address = AudioObjectPropertyAddress {
            mSelector: kAudioDevicePropertyStreamConfiguration,
            mScope: scope,
            mElement: kAudioObjectPropertyElementMaster,
        };
    
        let mut size = std::mem::size_of::<AudioBufferList>();
        let mut buffer_list: AudioBufferList = unsafe { std::mem::zeroed() };
        let status = unsafe {
            AudioObjectGetPropertyData(
                device_id,
                &address,
                0,
                std::ptr::null(),
                &mut size as *mut _ as *mut u32,
                &mut buffer_list as *mut _ as *mut std::ffi::c_void,
            )
        };

        if status != coreaudio::sys::noErr as i32 {
            return Ok(0);
        }
    
        let total_channels = (0..buffer_list.mNumberBuffers)
            .map(|i| unsafe { (*buffer_list.mBuffers.as_ptr().add(i as usize)).mNumberChannels })
            .sum();
    
        Ok(total_channels)
    }
    
    pub async fn create_dj_to_zoom_routing(&self) -> Result<()> {
        info!("Creating DJPro → Zoom routing");
        
        let devices = self.devices.read().await;
        
        // Find DJPro output device
        let dj_device = devices.iter()
            .find(|d| d.name.to_lowercase().contains("djpro") || 
                     d.name.to_lowercase().contains("dj pro"))
            .ok_or_else(|| anyhow!("DJPro output device not found"))?;
        
        // Find Zoom input device or create Ark virtual device
        let zoom_device = devices.iter()
            .find(|d| d.name.to_lowercase().contains("zoom") || 
                     d.is_ark_device)
            .ok_or_else(|| anyhow!("Zoom input device or Ark virtual device not found"))?;
        
        // Create the routing
        self.create_route(&dj_device.name, &zoom_device.name, 2).await?;
        
        info!("✓ DJPro → Zoom routing created");
        Ok(())
    }
    
    pub async fn create_waveform_to_dj_routing(&self) -> Result<()> {
        info!("Creating Waveform → DJPro routing with IAC sync");
        
        let devices = self.devices.read().await;
        
        // Find Waveform device
        let waveform_device = devices.iter()
            .find(|d| d.name.to_lowercase().contains("waveform"))
            .ok_or_else(|| anyhow!("Waveform device not found"))?;
        
        // Find DJPro input device
        let dj_device = devices.iter()
            .find(|d| d.name.to_lowercase().contains("djpro") || 
                     d.name.to_lowercase().contains("dj pro"))
            .ok_or_else(|| anyhow!("DJPro input device not found"))?;
        
        // Create the main audio routing
        self.create_route(&waveform_device.name, &dj_device.name, 2).await?;
        
        // Setup IAC sync (Inter-App Communication for sync)
        self.setup_iac_sync().await?;
        
        info!("✓ Waveform → DJPro routing with IAC sync created");
        Ok(())
    }
    
    async fn setup_iac_sync(&self) -> Result<()> {
        info!("Setting up IAC sync for tempo synchronization");
        
        // Find IAC driver
        let devices = self.devices.read().await;
        let iac_device = devices.iter()
            .find(|d| d.name.to_lowercase().contains("iac"))
            .ok_or_else(|| anyhow!("IAC Driver not found - enable in Audio MIDI Setup"))?;
        
        info!("Found IAC device: {}", iac_device.name);
        
        // IAC sync would typically involve MIDI clock sync
        // This is a placeholder for the actual sync implementation
        Ok(())
    }
    
    pub async fn create_custom_route(&self, from: &str, to: &str, channels: u32) -> Result<()> {
        self.create_route(from, to, channels).await
    }
    
    async fn create_route(&self, from: &str, to: &str, channels: u32) -> Result<()> {
        info!("Creating route: {} → {} ({} channels)", from, to, channels);

        let devices = self.devices.read().await;
        let from_device = devices
            .iter()
            .find(|d| d.name == from)
            .ok_or_else(|| anyhow!("Could not find 'from' device: {}", from))?;
        let to_device = devices
            .iter()
            .find(|d| d.name == to)
            .ok_or_else(|| anyhow!("Could not find 'to' device: {}", to))?;

        let mut audio_unit = AudioUnit::new(IOType::HalOutput)?;

        audio_unit.set_property(
            coreaudio::sys::kAudioOutputUnitProperty_CurrentDevice,
            Scope::Output,
            Element::Output,
            Some(&to_device.id),
        )?;
        audio_unit.set_property(
            coreaudio::sys::kAudioOutputUnitProperty_CurrentDevice,
            Scope::Input,
            Element::Input,
            Some(&from_device.id),
        )?;

        audio_unit.set_property(
            coreaudio::sys::kAudioOutputUnitProperty_EnableIO,
            Scope::Input,
            Element::Input,
            Some(&1u32),
        )?;
        audio_unit.set_property(
            coreaudio::sys::kAudioOutputUnitProperty_EnableIO,
            Scope::Output,
            Element::Output,
            Some(&1u32),
        )?;

        audio_unit.set_render_callback(|_args: Args<data::NonInterleaved<f32>>| {
            // A simple pass-through.
            Ok(())
        })?;

        audio_unit.initialize()?;
        audio_unit.start()?;

        let route = AudioRoute {
            from: from.to_string(),
            to: to.to_string(),
            channels,
            active: true,
        };

        let route_id = format!("{} -> {}", from, to);
        self.active_routes.lock().unwrap().push(route);
        self.audio_units
            .lock()
            .unwrap()
            .insert(route_id, audio_unit);

        Ok(())
    }
    
    pub async fn stop_all_routing(&self) -> Result<()> {
        info!("Stopping all audio routing");
        
        let mut routes = self.active_routes.lock().unwrap();
        for route in routes.iter_mut() {
            route.active = false;
        }
        routes.clear();
        
        // Stop all audio units
        let mut audio_units = self.audio_units.lock().unwrap();
        for (name, mut unit) in audio_units.drain() {
            info!("Stopping audio unit: {}", name);
            let _ = unit.stop();
        }
        
        info!("✓ All routing stopped");
        Ok(())
    }
    
    pub async fn get_routing_status(&self) -> Result<RoutingStatus> {
        let active_routes = self.active_routes.lock().unwrap().clone();
        let devices = self.devices.read().await;
        let ark_devices = devices.iter()
            .filter(|d| d.is_ark_device)
            .cloned()
            .collect();
        
        Ok(RoutingStatus {
            active_routes,
            ark_devices,
        })
    }
}