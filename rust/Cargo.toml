[package]
name = "ark-loopback"
version = "0.1.0"
edition = "2021"

[dependencies]
coreaudio = { package = "coreaudio-rs", version = "0.11" }
clap = { version = "4.0", features = ["derive"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"

[lib]
name = "ark_loopback"
path = "src/lib.rs"

[[bin]]
name = "ark"
path = "src/main.rs"
