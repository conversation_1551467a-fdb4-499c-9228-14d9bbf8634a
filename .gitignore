.DS_Store
/.build
/Packages
xcuserdata/
DerivedData/
.swiftpm/configuration/registries.json
.swiftpm/xcode/package.xcworkspace/contents.xcworkspacedata
.netrc
.aider*

# Build artifacts
*.o
*.dSYM
*.build
build/

# Rust artifacts
rust/target/
**/target/
*.rlib

# Xcode artifacts
*.xcodeproj/xcuserdata/
*.xcworkspace/xcuserdata/
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xcuserstate
*.xccheckout

# macOS artifacts
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Thumbnails
._.Trashes
.Spotlight-V100
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Temporary files
*.tmp
*.temp
*~
.#*
rust/
