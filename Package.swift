// swift-tools-version: 6.1
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "WavyAudio",
    platforms: [
        .macOS(.v14),
        .iOS(.v17),
        .visionOS(.v1)
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "WavyCore",
            targets: ["WavyCore"]
        ),
    ],
    dependencies: [
        // Add dependencies here, e.g., for CLI parsing
        .package(url: "https://github.com/apple/swift-argument-parser.git", from: "1.2.0"),
        .package(url: "https://github.com/rnine/SimplyCoreAudio.git", from: "4.0.0"),
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "WavyCore",
            dependencies: [
                "ArgumentParser",
                "SimplyCoreAudio",
            ],
            path: "Sources/WavyCore",
            swiftSettings: [
                .enableUpcomingFeature("StrictConcurrency"),
                .enableUpcomingFeature("ApproachableConcurrency"),
                .enableUpcomingFeature("DefaultActorIsolation"),
                .enableUpcomingFeature("MemberImportVisibility")
            ]
        ),
        .testTarget(
            name: "WavyCoreTests",
            dependencies: ["WavyCore"],
            path: "Tests/WavyCoreTests"
        ),
    ]
)
