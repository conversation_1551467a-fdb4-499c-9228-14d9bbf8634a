// swift-tools-version: 6.1
// The swift-tools-version declares the minimum version of Swift required to build this package.
// WWDC 2025 Compliance: Using Swift 6.1 with all upcoming features enabled

import PackageDescription

let package = Package(
    name: "WavyAudio",
    platforms: [
        .macOS(.v15)  // macOS 26.0 (Sequoia) - macOS only target
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "Wavy<PERSON>ore",
            targets: ["WavyCore"]
        ),
    ],
    dependencies: [
        // Add dependencies here, e.g., for CLI parsing
        .package(url: "https://github.com/apple/swift-argument-parser.git", from: "1.2.0"),
        .package(url: "https://github.com/rnine/SimplyCoreAudio.git", from: "4.0.0"),
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "<PERSON>avy<PERSON><PERSON>",
            dependencies: [
                .product(name: "ArgumentParser", package: "swift-argument-parser"),
                .product(name: "SimplyCoreAudio", package: "SimplyCoreAudio"),
            ],
            path: "Sources/WavyCore",
            swiftSettings: [
                // WWDC 2025: Swift 6.2 upcoming features
                .enableUpcomingFeature("StrictConcurrency"),
                .enableUpcomingFeature("ApproachableConcurrency"),
                .enableUpcomingFeature("DefaultActorIsolation"),
                .enableUpcomingFeature("MemberImportVisibility"),
                .enableUpcomingFeature("StrictMemorySafety"),
                // Performance optimizations
                .unsafeFlags(["-whole-module-optimization"], .when(configuration: .release))
            ]
        ),
        .testTarget(
            name: "WavyCoreTests",
            dependencies: ["WavyCore"],
            path: "Tests/WavyCoreTests",
            swiftSettings: [
                // Enable same features for tests
                .enableUpcomingFeature("StrictConcurrency"),
                .enableUpcomingFeature("ApproachableConcurrency"),
                .enableUpcomingFeature("DefaultActorIsolation"),
                .enableUpcomingFeature("MemberImportVisibility"),
                .enableUpcomingFeature("StrictMemorySafety")
            ]
        ),
    ]
)
