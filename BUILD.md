# Build Instructions for WavyAudio

This project is a **macOS application** built with Xcode, utilizing Swift/SwiftUI, Core Audio, and Metal. The project uses Xcode's build system for proper macOS app compilation.

## Project Structure

- `WavyAudio.xcodeproj`: Main Xcode project file
- `WavyAudio/`: The main SwiftUI application source code
- `Sources/WavyCore/`: Swift Package with Core Audio utilities
- `WavyAudio/Shaders/`: Metal shader files for GPU acceleration
- `rust/`: Optional Rust components (built separately)

## Build Process Overview

**Primary Build Method: Use Xcode**
1.  **Xcode Project:** The main `WavyAudio.xcodeproj` handles all compilation
2.  **Swift Package:** `WavyCore` is automatically built by Xcode
3.  **Metal Shaders:** Compiled by Xcode's Metal compiler
4.  **App Bundle:** Creates proper macOS `.app` with entitlements and resources

## Recommended Build Methods

### Method 1: Use the Build Script (Recommended)
```bash
# Quick build with WWDC 2025 optimizations
./build_wwdc2025.sh

# Build with tests
./build_wwdc2025.sh --test

# Create archive for distribution
./build_wwdc2025.sh --archive
```

### Method 2: Xcode GUI
```bash
# Open in Xcode
open WavyAudio.xcodeproj

# Then build with Cmd+B or Product → Build
```

### Method 3: Command Line (xcodebuild)
```bash
# Debug build
xcodebuild -project WavyAudio.xcodeproj -scheme WavyAudio -configuration Debug build

# Release build
xcodebuild -project WavyAudio.xcodeproj -scheme WavyAudio -configuration Release build

# Clean build
xcodebuild -project WavyAudio.xcodeproj -scheme WavyAudio clean build
```

## Optional: Rust Components

If you want to build the optional Rust components separately:

```bash
cd rust/
cargo build --release
```

**Note:** The Rust components are optional and not required for the main macOS application to build and run.

## Common Issues and Troubleshooting

### Build Errors
-   **"No such file or directory":** Ensure you're using `xcodebuild` with the `-project` flag
-   **Metal Shader Errors:** Check that `.metal` files are included in the Xcode project
-   **Swift Package Issues:** Clean derived data: `rm -rf ~/Library/Developer/Xcode/DerivedData`
-   **Core Audio Permissions:** Ensure microphone permissions are granted in System Preferences

### Performance Issues
-   **Slow Builds:** Use `COMPILER_INDEX_STORE_ENABLE=NO` for faster debug builds
-   **Memory Usage:** Use `ONLY_ACTIVE_ARCH=YES` for single-architecture builds during development

## Cleaning the Project

```bash
# Clean Xcode project (recommended)
xcodebuild clean -project WavyAudio.xcodeproj -scheme WavyAudio

# Or from Xcode: Product → Clean Build Folder (Cmd+Shift+K)

# Clean derived data (if needed)
rm -rf ~/Library/Developer/Xcode/DerivedData/WavyAudio-*

# Optional: Clean Rust components
cd rust/ && cargo clean
```

## Requirements

- **Xcode 15.2+** (Xcode 16+ recommended for WWDC 2025 features)
- **macOS 14.0+** (macOS 15.0+ recommended)
- **Apple Silicon or Intel Mac**
- **Core Audio permissions** (microphone access)
