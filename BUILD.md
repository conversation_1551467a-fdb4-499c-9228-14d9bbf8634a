# Build Instructions for WavyAudio

This project utilizes a multi-language architecture, combining Swift/SwiftUI, C++, and Rust. Building the entire project requires orchestrating these different components.

## Project Structure

- `WavyAudio/`: The main SwiftUI application.
- `Sources/WavyCore/`: Contains C++ source files that are bridged to Swift.
- `rust/`: Contains the Rust project, which can be built into a library.

## Build Process Overview

1.  **Rust Component:** The Rust project needs to be built first to produce a library that the Swift project can link against.
2.  **C++ Component:** The C++ files in `Sources/WavyCore/` are compiled as part of the Swift Package.
3.  **Swift/SwiftUI Application:** The main Xcode project compiles the Swift and SwiftUI code and links against the Rust and C++ libraries.

## Detailed Build Steps

### 1. Build the Rust Library

Navigate to the `rust/` directory and build the Rust project. This will typically produce a static or dynamic library (`.a` or `.dylib`) in the `rust/target/` directory.

```bash
cd rust/
cargo build --release
```

**Note:** Ensure the output library is placed in a location accessible by Xcode. You might need to configure an Xcode "Run Script Phase" to copy this library to a specific location (e.g., `$(PROJECT_DIR)/Libraries/` or directly into the build products directory).

**Note on `rust/Proaudio/`:** This directory contains a nested Xcode project. Its purpose is currently unclear and it is not directly integrated into the main `WavyAudio` build process. It may be an old experiment or a separate utility. Consider cleaning it up or documenting its specific role if it is still relevant.

### 2. Build the Swift Package (WavyCore)

The `WavyCore` Swift Package (containing C++ sources) is typically built automatically by Xcode when the main `WavyAudio.xcodeproj` is built, as long as it's correctly added as a package dependency.

### 3. Build the Main WavyAudio Application

Open `WavyAudio.xcodeproj` in Xcode and build the project. Xcode will handle the compilation of Swift/SwiftUI code and link against the Rust library (if configured correctly) and the C++ components from `WavyCore`.

```bash
open WavyAudio.xcodeproj
```

Then, build from within Xcode (Cmd+B).

## Common Issues and Troubleshooting

-   **Linking Errors:** If you encounter linking errors related to the Rust library, ensure that:
    -   The Rust library is built for the correct architecture (e.g., `aarch64-apple-darwin` for Apple Silicon).
    -   The library's path is correctly specified in Xcode's "Build Settings" -> "Library Search Paths" and "Other Linker Flags".
    -   The library is copied to the correct location during the build process (e.g., via a "Run Script Phase").
-   **Bridging Header Issues:** For C++ interoperability, ensure `WavyAudio-Bridging-Header.h` is correctly configured in Xcode's "Build Settings" -> "Objective-C Bridging Header".

## Cleaning the Project

To perform a clean build, you may need to clean each component separately:

```bash
# Clean Rust project
cd rust/
cargo clean

# Clean Xcode project (from Xcode menu: Product -> Clean Build Folder)
# Or via command line:
xcodebuild clean -project WavyAudio.xcodeproj -alltargets
```
