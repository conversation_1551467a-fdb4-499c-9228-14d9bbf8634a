# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

WavyAudio is a professional Core Audio HAL application for macOS providing real-time audio device management and routing capabilities. Built with modern SwiftUI 6.2, Metal acceleration, and direct Core Audio integration for low-latency audio processing.

**Project Status**: 🔧 BUILD IN PROGRESS - Compilation errors being resolved  
**Last Updated**: July 10, 2025  
**UI Enhancement Status**: ✅ COMPLETE - Professional accessibility, performance & animation optimizations  
**Professional Audio Compatibility**: ✅ COMPLETE - Dynamic resource management with SoundSource integration  
**Business Package**: ✅ COMPLETE - PRD, landing page, business plan created  
**Current Status**: ⚠️ BUILD ERRORS - AudioProcessingView and minor compilation issues remaining  
**Last Build**: ❌ FAILED - July 10, 2025 - Fixing remaining Swift compilation errors

## Architecture

Modern macOS app following WWDC 2025 best practices:

- **Swift 6.2**: Concurrency with async/await, @Observable pattern, MainActor isolation
- **SwiftUI 6.2**: NavigationSplitView, Canvas API, Metal-accelerated rendering
- **Core Audio HAL**: Direct integration without wrapper dependencies
- **Metal 3.1**: GPU acceleration for UI rendering and visual effects
- **WWDC Compliance**: Accessibility, performance monitoring, professional audio standards

## Development Commands

### Essential Build Commands
```bash
# Quick build with optimizations
xcodebuild -project WavyAudio.xcodeproj -scheme WavyAudio -configuration Debug build \
           COMPILER_INDEX_STORE_ENABLE=NO \
           ONLY_ACTIVE_ARCH=YES

# Clean and rebuild
xcodebuild clean -scheme WavyAudio
rm -rf ~/Library/Developer/Xcode/DerivedData/WavyAudio*

# Set correct Xcode toolchain
sudo xcode-select -s /Applications/Xcode-beta.app/Contents/Developer
```

## Recent Development History

### July 10, 2025 - Build Error Resolution & Architecture Updates
**Status**: 🔧 Resolving final compilation errors

**Issues Fixed**:
1. ✅ **Duplicate AudioEngineManager**: Removed Services/AudioEngineManager.swift, kept Core version
2. ✅ **Metal Shader Errors**: Fixed mesh shader syntax in AudioProcessing.metal
3. ✅ **Observable Pattern Migration**: Updated AudioProcessingView from @ObservedObject to @Observable
4. ✅ **Async System Conflicts**: Added proper async handling for detectSystemConflicts()

**Remaining Issues**:
1. ⚠️ **AudioProcessingView**: Missing updateEffectParameters method implementation
2. ⚠️ **Engine Status Check**: Need to use engineStatus enum instead of isRunning property
3. ⚠️ **AudioMemoryManager**: Sendable conformance warnings for unsafe pointers
4. ⚠️ **AudioConnection**: @preconcurrency attribute warnings

**Architecture Changes**:
- Migrated from `ObservableObject` to `@Observable` macro pattern throughout
- Fixed Core Audio HAL async/await patterns
- Resolved Metal 3.5 shader compilation issues

### January 8, 2025 - Comprehensive Code Audit & Build Fixes
**Code Audit Results**: 
- ✅ No TODO/FIXME markers found
- ✅ Strong WWDC compliance patterns maintained
- ✅ Major compilation issues identified and resolved
- ✅ Added professional real-time audio flow monitoring system

**Critical Issues Fixed**:
1. **SystemConflict Type Missing**: ✅ Created comprehensive `SystemConflict.swift` model with conflict detection
2. **Syntax Error**: ✅ Removed stray "at--" line in `AudioEngineManager.swift`
3. **Import Statement Placement**: ✅ Fixed misplaced `import os` in `AudioRoutingCanvas.swift`
4. **Duplicate Type Definitions**: ✅ Resolved ConflictType/ConflictSeverity duplicates in SharedTypes.swift
5. **Swift 6 Observable Pattern**: ✅ Updated @StateObject to @State for @Observable classes
6. **Type Ambiguity**: ✅ Renamed AudioConnection to AudioFlowConnection in monitoring system
7. **Missing Error Types**: ✅ Added AudioProcessingError and AudioEngineError.halInitializationFailed
8. **Property Conflicts**: ✅ Fixed 'error' case name conflict by renaming to 'errorLevel'
9. **Real-time Audio Monitoring**: ✅ Successfully implemented `AudioFlowMonitor` with AVAudioSinkNode
10. **Particle System**: ✅ Added high-performance `AudioParticleSystem` with pre-allocated pool

### January 7, 2025 - SwiftUI Environment Crash Resolution
**Issue**: Critical SwiftUI crash in `EnvironmentValues.subscript.getter` within AppKitToolbarStrategy on macOS Sequoia
**Root Cause**: SwiftUI AppKit toolbar bridge attempting to access environment values before proper initialization
**Resolution**: 
- Fixed Core Audio HAL implementation (memory allocation, API parameter issues)
- Enhanced environment object injection in `WavyAudioApp.swift`
- Temporarily disabled toolbar due to SwiftUI framework bug
- Added comprehensive logging to `DeviceManager` and `AudioDiagnostics`

## Key Features Implemented

### Accessibility Excellence (WWDC Compliant)
- **Reduced Motion Support**: Conditional animations with `@Environment(\.accessibilityReduceMotion)`
- **VoiceOver Integration**: Comprehensive accessibility labels and actions
- **Assistive Technology**: Switch Control and Voice Control support

### Performance Innovation
- **Spring Animation System**: Natural physics-based motion with WWDC25-266 compliance
- **Level-of-Detail Rendering**: Adaptive detail levels based on zoom and performance
- **SIMD Connection Batching**: Vectorized animation calculations for 4x performance boost
- **Viewport Culling**: Only render visible elements for large audio routing canvases
- **Adaptive Frame Rate**: 120Hz ProMotion → 60fps → 30fps → 15fps based on system load
- **Dirty Region Tracking**: Selective canvas updates with Metal GPU optimization

### Professional Workflows
- **Complete Undo/Redo System**: Action history with keyboard shortcuts (Cmd+Z, Cmd+Shift+Z)
- **Professional Mode**: Adaptive resource management for SoundSource, DJ Pro, Waveform compatibility
- **Audio Workgroups**: WWDC22-110363 compliant thread management

## Core Audio Integration

**Direct HAL Implementation**:
- Device enumeration using `kAudioHardwarePropertyDevices`
- Real-time processing with vDSP vectorization
- Thread affinity configuration for audio threads
- Conflict detection for professional audio apps

**Critical APIs**:
- `CoreAudioHALManager`: Full HAL interface and device detection
- `AudioDiagnostics`: Professional system monitoring with adaptive intervals
- `AudioEngineManager`: Main engine with @Observable state management

## WWDC References

**Key Sessions Implemented**:
- WWDC25-268: Swift 6 concurrency patterns (@Observable, MainActor isolation)
- WWDC25-319: SwiftUI essentials (Canvas API, performance optimization)
- WWDC25-266: Liquid Glass design system (dynamic materials, adaptive interfaces)
- WWDC22-110363: Audio Workgroups (Thread management, cooperative scheduling)
- WWDC19-608: Metal for Pro Apps (GPU acceleration, spring animations)
- WWDC21-10036: Sound Analysis (Real-time monitoring, diagnostic systems)
- WWDC24-10169: Swift 6 migration (Sendable conformance, data race elimination)

### WWDC Transcript Research Tool

For proper research of WWDC sessions, use the transcript analysis tool:

```bash
# Search for specific topics across all sessions
python3 /Users/<USER>/wwdc-transcript-tool.py --search "SwiftUI environment crash"
python3 /Users/<USER>/wwdc-transcript-tool.py --search "AppKit toolbar SwiftUI"

# Research specific session transcripts
python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-268  # Swift 6 concurrency
python3 /Users/<USER>/wwdc-transcript-tool.py --session 2025-319  # SwiftUI essentials
python3 /Users/<USER>/wwdc-transcript-tool.py --session 2022-110363  # Audio Workgroups

# Multi-year search for evolving topics
python3 /Users/<USER>/wwdc-transcript-tool.py --search "Core Audio HAL" --years 2019,2020,2021,2022,2023,2024,2025

# Framework-specific research
python3 /Users/<USER>/wwdc-transcript-tool.py --search "Metal performance" --framework Metal
python3 /Users/<USER>/wwdc-transcript-tool.py --search "audio latency" --framework CoreAudio
```

**Research Best Practices**:
- Always search across multiple years for API evolution
- Use specific technical terms rather than general concepts
- Cross-reference session numbers with implementation details
- Verify code patterns against latest WWDC examples

## Professional Audio Compatibility

**Implemented Features**:
- ✅ Dynamic resource management with app detection
- ✅ Adaptive monitoring intervals (5s → 15s/30s when professional apps detected)
- ✅ Audio Workgroups thread priority management
- ✅ Real-time conflict detection and system diagnostics

**Integration Analysis**:
- ✅ SoundSource cooperation mode with HAL sharing protocols
- ✅ Widget vs Menu Bar evaluation (WidgetKit preferred for system integration)
- ✅ NotchosPro compatibility analysis for menu bar enhancement
- ✅ Emergency audio device handling with virtual driver safety

**System Log Analysis**:
- First launch scenarios: Permission handling, device discovery
- Virtual audio driver management: Safe creation and deletion protocols
- Emergency headphone removal: Automatic fallback to built-in speakers
- Professional app cooperation: Reduced monitoring when Logic Pro/SoundSource detected

## Performance Requirements

**Targets**:
- Audio latency: <10ms round-trip
- CPU overhead: <20% baseline, <5% with professional apps
- Frame rate: 60fps with graceful degradation to 30fps
- Memory usage: Efficient texture management with Metal

## Troubleshooting

### Metal Compilation Issues
If Metal shaders fail to compile:
```bash
xcodebuild -downloadComponent MetalToolchain
```

### Build Performance
For faster builds:
```bash
# Disable indexing and use single architecture
COMPILER_INDEX_STORE_ENABLE=NO ONLY_ACTIVE_ARCH=YES
```

### Professional Audio Conflicts
App automatically detects and adapts to:
- SoundSource (HAL cooperation mode)
- DJ Pro (44.1kHz preference)
- Waveform/Logic Pro (recording session detection)

## File Structure

**Core Implementation**:
- `CoreAudioHALManager.swift`: Main HAL interface with fixed memory management
- `AudioDiagnostics.swift`: Professional monitoring service with initialization logging
- `AudioEngineManager.swift`: @Observable audio engine with device enumeration
- `UndoRedoManager.swift`: Action history system
- `AudioRoutingCanvas.swift`: Metal-accelerated Canvas with accessibility

**UI Layer**:
- `ContentView.swift`: NavigationSplitView with environment injection
- `DeviceListSidebar.swift`: Device enumeration with diagnostics integration
- `DeviceManager.swift`: @Observable device management with initialization logging
- `MetalRenderer.swift`: GPU-accelerated visualization (✅ Fixed parameter issues)

**Services**:
- `AudioProcessor.swift`: Real-time audio processing
- `MetalFXOptimizer.swift`: Metal performance optimization
- `UndoRedoManager.swift`: Action history and keyboard shortcuts

**Environment Setup**:
- `WavyAudioApp.swift`: App entry point with comprehensive @Environment injection
- All @Observable objects properly provided to environment hierarchy
- Toolbar temporarily disabled due to SwiftUI AppKit bridge bug

## Business Documentation

**Complete Package Created** (`/Users/<USER>/Desktop/WavyAudio-Launch/`):
- **PRD.md**: Comprehensive Product Requirements Document
- **index.html**: Marketing landing page with Apple design principles
- **business-plan.html**: Interactive KPI dashboard with Chart.js visualizations

**Market Positioning**:
- Target Price: $49 (between SoundSource $39 and Loopback $109)
- Revenue Target: $122,500 gross in Year 1 (2,500 units)
- Market Focus: Visual-first audio routing with accessibility compliance

## Current Development Notes

### July 10, 2025 - Active Build Issues
**AudioProcessingView.swift**:
- Need to implement `updateEffectParameters` method in AudioEngineManager
- Replace `isRunning` property check with `engineStatus == .running`
- Fix preview return statement issue

**AudioMemoryManager.swift**:
- Address Sendable conformance warnings for UnsafeMutableRawPointer
- Remove unnecessary `nonisolated(unsafe)` attribute

**Next Steps**:
1. Fix remaining compilation errors in AudioProcessingView
2. Address Sendable conformance warnings
3. Test build and verify functionality
4. Update documentation with successful build status

### Known SwiftUI Issues
- **Toolbar Bug**: SwiftUI AppKitToolbarStrategy crashes on macOS Sequoia when accessing environment values
- **Temporary Solution**: Toolbars disabled in ContentView.swift and DeviceListSidebar.swift
- **Future Fix**: Re-enable when Apple resolves environment access in toolbar bridge
- **Alternative**: Consider native AppKit NSToolbar implementation if needed

### Development Workflow
1. **Priority 1**: Fix compilation errors before any feature development
2. Always check Console.app for initialization logs after app launch
3. Test environment object injection when making UI changes
4. Monitor Core Audio HAL manager for device enumeration issues
5. Use WWDC transcript tool for researching best practices

This project represents a modern approach to professional audio routing on macOS, leveraging the latest Swift and SwiftUI features while maintaining compatibility with professional audio workflows.
